#!/usr/bin/env python3
"""
Debug booking creation step by step
"""

from app import create_app
from models import db, User, Booking, AvailabilitySlot
from datetime import datetime, timedelta
import traceback

def debug_booking_creation():
    app = create_app()
    with app.app_context():
        print("=== DEBUGGING BOOKING CREATION ===")
        print()
        
        # Step 1: Get users
        john = User.query.filter_by(email='<EMAIL>').first()
        vivek = User.query.filter_by(email='<EMAIL>').first()
        
        if not john or not vivek:
            print("❌ Users not found")
            return
            
        print(f"✅ John: {john.name} (ID: {john.id})")
        print(f"✅ Vivek: {vivek.name} (ID: {vivek.id})")
        print()
        
        # Step 2: Update login times
        john.last_login = datetime.utcnow()
        vivek.last_login = datetime.utcnow()
        db.session.commit()
        print(f"✅ Updated login times")
        print()
        
        # Step 3: Test authentication logic
        print("Step 3: Testing authentication logic...")
        recent_time = datetime.utcnow() - timedelta(hours=1)
        print(f"Recent time threshold: {recent_time}")
        
        recent_users = User.query.filter(
            User.status == 'approved',
            User.last_login >= recent_time
        ).all()
        
        print(f"Found {len(recent_users)} recent users:")
        for user in recent_users:
            expected_token = f"auth_token_{user.id}_{user.email}"
            print(f"  - {user.name}: {expected_token}")
        print()
        
        # Step 4: Test booking creation
        print("Step 4: Testing booking creation...")
        
        # Find available slot
        available_slot = AvailabilitySlot.query.filter_by(
            coach_id=vivek.id,
            status='free'
        ).first()
        
        if not available_slot:
            print("❌ No available slots found")
            return
            
        print(f"✅ Found available slot: {available_slot.date} {available_slot.start_time}-{available_slot.end_time}")
        
        try:
            # Create booking
            booking = Booking(
                coach_id=vivek.id,
                client_id=john.id,
                availability_slot_id=available_slot.id,
                date=available_slot.date,
                start_time=available_slot.start_time,
                end_time=available_slot.end_time,
                session_type='virtual',
                specialization='Debug Test',
                notes='Debug booking creation test',
                status='pending'
            )
            
            print("✅ Booking object created")
            
            db.session.add(booking)
            print("✅ Booking added to session")
            
            db.session.commit()
            print(f"✅ Booking committed with ID: {booking.id}")
            
            # Test to_dict()
            booking_dict = booking.to_dict()
            print(f"✅ Booking to_dict() works")
            print(f"  Coach: {booking_dict.get('coach_name')}")
            print(f"  Client: {booking_dict.get('client_name')}")
            
        except Exception as e:
            print(f"❌ Error creating booking: {str(e)}")
            traceback.print_exc()
            db.session.rollback()
            
        print()
        print("=== DEBUG COMPLETE ===")

if __name__ == "__main__":
    debug_booking_creation()
