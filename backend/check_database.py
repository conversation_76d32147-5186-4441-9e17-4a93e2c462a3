#!/usr/bin/env python3
"""
Check the current state of the database and verify if users are being added
"""

import sqlite3
import os
from app import create_app
from models import db, User, <PERSON><PERSON>

def check_database_file():
    """Check if database file exists and its contents"""
    print("🔍 Checking Database File Status:")
    print("=" * 50)
    
    # Check if database file exists
    db_path = 'coachcentral.db'
    if os.path.exists(db_path):
        print(f"✅ Database file exists: {db_path}")
        file_size = os.path.getsize(db_path)
        print(f"📊 File size: {file_size} bytes")
        
        # Check with SQLite directly
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # List all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"📋 Tables found: {[table[0] for table in tables]}")
            
            # Check users table if it exists
            if any('user' in table[0].lower() for table in tables):
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%user%'")
                user_tables = cursor.fetchall()
                
                for table in user_tables:
                    table_name = table[0]
                    print(f"\n📊 Table: {table_name}")
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   Records: {count}")
                    
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                        records = cursor.fetchall()
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = [col[1] for col in cursor.fetchall()]
                        print(f"   Columns: {columns}")
                        print(f"   Sample records:")
                        for record in records:
                            print(f"     {record}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Error reading database file: {e}")
    else:
        print(f"❌ Database file does not exist: {db_path}")

def check_flask_database():
    """Check database through Flask app context"""
    print("\n🔍 Checking Database Through Flask App:")
    print("=" * 50)
    
    try:
        app = create_app()
        with app.app_context():
            # Create tables if they don't exist
            db.create_all()
            print("✅ Database tables created/verified")
            
            # Check users
            users = User.query.all()
            print(f"👥 Total users in database: {len(users)}")
            
            for user in users:
                print(f"  - ID {user.id}: {user.name} ({user.email}) - Status: {user.status}")
                print(f"    Created: {user.created_at}")
                if hasattr(user, 'last_login') and user.last_login:
                    print(f"    Last login: {user.last_login}")
            
            # Check admins
            admins = Admin.query.all()
            print(f"\n👨‍💼 Total admins in database: {len(admins)}")
            
            for admin in admins:
                print(f"  - ID {admin.id}: {admin.name} ({admin.email})")
            
            # Check database file after Flask operations
            print(f"\n📁 Database file exists after Flask operations: {os.path.exists('coachcentral.db')}")
            
    except Exception as e:
        print(f"❌ Error with Flask database: {e}")
        import traceback
        traceback.print_exc()

def test_user_creation():
    """Test creating a new user to see if it persists"""
    print("\n🧪 Testing User Creation:")
    print("=" * 50)
    
    try:
        app = create_app()
        with app.app_context():
            # Check if test user already exists
            test_email = "<EMAIL>"
            existing_user = User.query.filter_by(email=test_email).first()
            
            if existing_user:
                print(f"🔍 Test user already exists: {existing_user.name} ({existing_user.email})")
                print(f"   Status: {existing_user.status}")
            else:
                # Create a test user
                test_user = User(
                    name="Database Test User",
                    email=test_email,
                    phone="1234567890",
                    country="Test Country",
                    credential="Test Credential",
                    institution="Test Institution",
                    status="pending"
                )
                test_user.set_password("testpassword")
                
                db.session.add(test_user)
                db.session.commit()
                
                print(f"✅ Created test user: {test_user.name} (ID: {test_user.id})")
                print(f"   Email: {test_user.email}")
                print(f"   Status: {test_user.status}")
            
            # Verify the user exists
            verification_user = User.query.filter_by(email=test_email).first()
            if verification_user:
                print(f"✅ User verification successful: {verification_user.name}")
            else:
                print(f"❌ User verification failed - user not found after creation")
                
    except Exception as e:
        print(f"❌ Error testing user creation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("🎯 CoachCentral Database Check")
    print("=" * 60)
    
    # Check database file directly
    check_database_file()
    
    # Check through Flask
    check_flask_database()
    
    # Test user creation
    test_user_creation()
    
    # Final file check
    print(f"\n📁 Final database file check: {os.path.exists('coachcentral.db')}")
    if os.path.exists('coachcentral.db'):
        print(f"📊 Final file size: {os.path.getsize('coachcentral.db')} bytes")
