#!/usr/bin/env python3
"""
Initialize database and check if users are being added properly
"""

import os
import sqlite3
from app import create_app
from models import db, User, Admin

def main():
    print("🎯 Initializing and Checking Database")
    print("=" * 50)
    
    # Remove existing database to start fresh
    db_path = 'instance/coachcentral.db'
    if os.path.exists(db_path):
        print(f"🗑️  Removing existing database: {db_path}")
        os.remove(db_path)
    
    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    
    # Create Flask app and initialize database
    app = create_app()
    
    with app.app_context():
        print("🔧 Creating database tables...")
        db.create_all()
        
        # Check if tables were created
        print("📋 Checking created tables...")
        
        # Use SQLite to check tables
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"✅ Tables created: {tables}")
        conn.close()
        
        # Create admin user
        print("👨‍💼 Creating admin user...")
        existing_admin = Admin.query.filter_by(email='<EMAIL>').first()
        if not existing_admin:
            admin = Admin(
                name='Admin User',
                email='<EMAIL>'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ Admin user created")
        else:
            print("ℹ️  Admin user already exists")
        
        # Create test users
        print("👥 Creating test users...")
        
        test_users = [
            {
                'name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '1234567890',
                'country': 'USA',
                'credential': 'ACC Certified',
                'institution': 'ICF',
                'status': 'approved'
            },
            {
                'name': 'Jane Smith',
                'email': '<EMAIL>',
                'phone': '0987654321',
                'country': 'Canada',
                'credential': 'PCC In Progress',
                'institution': 'ICF',
                'status': 'pending'
            },
            {
                'name': 'Test Pending User',
                'email': '<EMAIL>',
                'phone': '5555555555',
                'country': 'UK',
                'credential': 'MCC Certified',
                'institution': 'ICF',
                'status': 'pending'
            }
        ]
        
        for user_data in test_users:
            existing_user = User.query.filter_by(email=user_data['email']).first()
            if not existing_user:
                user = User(
                    name=user_data['name'],
                    email=user_data['email'],
                    phone=user_data['phone'],
                    country=user_data['country'],
                    credential=user_data['credential'],
                    institution=user_data['institution'],
                    status=user_data['status']
                )
                user.set_password('password123')
                db.session.add(user)
                print(f"✅ Created user: {user_data['name']} ({user_data['status']})")
            else:
                print(f"ℹ️  User already exists: {user_data['name']}")
        
        db.session.commit()
        
        # Verify users were created
        print("\n📊 Database Status After Initialization:")
        print("-" * 40)
        
        users = User.query.all()
        print(f"👥 Total users: {len(users)}")
        for user in users:
            print(f"  - ID {user.id}: {user.name} ({user.email}) - {user.status}")
        
        admins = Admin.query.all()
        print(f"👨‍💼 Total admins: {len(admins)}")
        for admin in admins:
            print(f"  - ID {admin.id}: {admin.name} ({admin.email})")
        
        # Test signup process
        print("\n🧪 Testing Signup Process:")
        print("-" * 30)
        
        # Simulate a new user signup
        new_user_email = "<EMAIL>"
        existing = User.query.filter_by(email=new_user_email).first()
        
        if existing:
            print(f"🗑️  Removing existing test user: {existing.name}")
            db.session.delete(existing)
            db.session.commit()
        
        # Create new user (simulating signup)
        new_user = User(
            name="New Signup User",
            email=new_user_email,
            phone="1111111111",
            country="Test Country",
            credential="Test Credential",
            institution="Test Institution",
            status="pending"  # This is what signup should create
        )
        new_user.set_password("newpassword")
        
        db.session.add(new_user)
        db.session.commit()
        
        print(f"✅ New user created via signup simulation:")
        print(f"   Name: {new_user.name}")
        print(f"   Email: {new_user.email}")
        print(f"   Status: {new_user.status}")
        print(f"   ID: {new_user.id}")
        
        # Verify it's in the database
        verification = User.query.filter_by(email=new_user_email).first()
        if verification:
            print(f"✅ Verification successful: User found in database")
        else:
            print(f"❌ Verification failed: User not found in database")
    
    # Final database file check
    print(f"\n📁 Final database file status:")
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f"✅ Database file exists: {db_path}")
        print(f"📊 File size: {file_size} bytes")
        
        # Check with direct SQLite connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        print(f"👥 Users in database (direct SQLite): {user_count}")
        
        cursor.execute("SELECT id, name, email, status FROM user ORDER BY id")
        users = cursor.fetchall()
        print("📋 User list:")
        for user in users:
            print(f"  - ID {user[0]}: {user[1]} ({user[2]}) - {user[3]}")
        
        conn.close()
    else:
        print(f"❌ Database file does not exist: {db_path}")

if __name__ == '__main__':
    main()
