#!/usr/bin/env python3
"""
Test the booking fix
"""

import requests
import json
from app import create_app
from models import db, User
from datetime import datetime

def test_booking_fix():
    """Test the booking API with the authentication fix"""
    print("=== TESTING BOOKING FIX ===")
    
    # First, update a user's login time to make them "recent"
    app = create_app()
    with app.app_context():
        john = User.query.filter_by(email='<EMAIL>').first()
        if john:
            john.last_login = datetime.utcnow()
            db.session.commit()
            print(f'✅ Updated John\'s last login: {john.last_login}')
        else:
            print('❌ John not found')
            return
    
    # Test with a token that matches the format from the logs
    test_token = "6A4paRTtI7abcdefghijklmnopqrstuvwxyz"  # 32+ char token
    
    booking_data = {
        'coach_id': 3,  # Vivek's ID
        'date': '2025-08-15',
        'start_time': '14:00',
        'end_time': '16:00',
        'session_type': 'virtual',
        'specialization': 'Test Coaching',
        'notes': 'Testing the authentication fix'
    }
    
    print(f"Making booking request with token: {test_token[:10]}...")
    print(f"Booking data: {booking_data}")
    
    try:
        response = requests.post(
            'http://127.0.0.1:8000/api/bookings',
            headers={
                'Content-Type': 'application/json',
                'Authorization': test_token
            },
            json=booking_data,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 201:
            print("✅ Booking created successfully!")
            response_data = response.json()
            print(f"Booking ID: {response_data.get('booking', {}).get('id')}")
        elif response.status_code == 401:
            print("❌ Still getting 401 - authentication fix didn't work")
        elif response.status_code == 400:
            print("⚠️  Got 400 - might be a data validation issue")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_booking_fix()
