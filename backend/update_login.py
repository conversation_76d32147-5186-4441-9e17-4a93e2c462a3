#!/usr/bin/env python3
"""
Update user login time for testing
"""

from app import create_app
from models import db, User
from datetime import datetime

def update_login():
    app = create_app()
    with app.app_context():
        john = User.query.filter_by(email='<EMAIL>').first()
        if john:
            john.last_login = datetime.utcnow()
            db.session.commit()
            print(f'✅ Updated John\'s last login to: {john.last_login}')
            print(f'✅ Expected token: auth_token_{john.id}_{john.email}')
            return True
        else:
            print('❌ John not found')
            return False

if __name__ == "__main__":
    update_login()
