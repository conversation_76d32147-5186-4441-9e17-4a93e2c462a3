from flask import Flask, request, jsonify, session, send_from_directory
from flask_cors import CORS
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
import os
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import uuid

from config import Config
from models import db, Admin, User, PendingUser, ApprovedUser, CoachAvailability, AvailabilitySlot, Booking

# Simple in-memory token storage (in production, use Redis or database)
user_tokens = {}

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Ensure session configuration is applied for cross-origin requests
    app.config.update(
        SESSION_COOKIE_SECURE=False,
        SESSION_COOKIE_HTTPONLY=False,
        SESSION_COOKIE_SAMESITE='Lax',
        SESSION_COOKIE_DOMAIN=None,
        SESSION_COOKIE_PATH='/',
        SESSION_PERMANENT=False,
        REMEMBER_COOKIE_SECURE=False,
        REMEMBER_COOKIE_HTTPONLY=False,
        REM<PERSON>BER_COOKIE_SAMESITE='Lax'
    )
    
    # Initialize extensions
    db.init_app(app)

    # Configure CORS for session persistence with explicit settings
    CORS(app,
         supports_credentials=True,
         origins=["http://localhost:5173", "http://localhost:5174", "http://127.0.0.1:5173", "http://127.0.0.1:5174", "http://localhost:3000"],
         allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials", "Cookie"],
         expose_headers=["Set-Cookie"],
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         send_wildcard=False,
         vary_header=True)
    
    # Initialize Flask-Login with session configuration
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'admin_login'
    login_manager.session_protection = "strong"
    login_manager.remember_cookie_secure = False
    login_manager.remember_cookie_httponly = False
    login_manager.remember_cookie_samesite = 'Lax'
    
    @login_manager.user_loader
    def load_user(user_id):
        # Check if it's an admin
        admin = Admin.query.get(int(user_id))
        if admin:
            return admin

        # Check new unified User model
        user = User.query.get(int(user_id))
        if user and user.status == 'approved':
            return user

        # Fallback to legacy ApprovedUser for backward compatibility
        return ApprovedUser.query.get(int(user_id))
    
    # Create upload directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Root route
    @app.route('/')
    def home():
        return jsonify({
            'message': 'CoachCentral Backend API',
            'status': 'running',
            'version': '1.0.0',
            'timestamp': datetime.utcnow().isoformat(),
            'available_endpoints': {
                'health': '/api/health',
                'admin_login': '/api/admin/login',
                'user_login': '/api/user/login',
                'signup': '/api/signup',
                'admin_endpoints': [
                    '/api/admin/pending-users',
                    '/api/admin/approved-users',
                    '/api/admin/approve-user/<id>',
                    '/api/admin/reject-user/<id>'
                ],
                'booking_endpoints': [
                    '/api/coaches/available',
                    '/api/bookings',
                    '/api/bookings/pending',
                    '/api/user/availability'
                ]
            }
        })
    
    # User Registration API
    @app.route('/api/signup', methods=['POST'])
    def signup():
        try:
            # Handle form data with file upload
            name = request.form.get('name')
            email = request.form.get('email')
            password = request.form.get('password')
            phone = request.form.get('phone')
            credential = request.form.get('credential')
            institution = request.form.get('institution')
            country = request.form.get('country')
            
            # Validate required fields
            if not all([name, email, password, phone, credential, institution, country]):
                return jsonify({'error': 'All fields are required'}), 400
            
            # Check if email already exists in users table (any status)
            existing_user = User.query.filter_by(email=email).first()

            if existing_user:
                if existing_user.status == 'pending':
                    return jsonify({'error': 'Email already registered. Application is pending approval.'}), 400
                elif existing_user.status == 'approved':
                    return jsonify({'error': 'Email already registered. Please use the login page.'}), 400
                elif existing_user.status == 'declined':
                    return jsonify({'error': 'Previous application was declined. Please contact support.'}), 400

            # Also check legacy tables for backward compatibility
            existing_pending = PendingUser.query.filter_by(email=email).first()
            existing_approved = ApprovedUser.query.filter_by(email=email).first()

            if existing_pending or existing_approved:
                return jsonify({'error': 'Email already registered'}), 400
            
            # Handle file upload
            resume_filename = None
            if 'resume' in request.files:
                file = request.files['resume']
                if file and file.filename and Config.allowed_file(file.filename):
                    # Generate unique filename
                    filename = secure_filename(file.filename)
                    unique_filename = f"{uuid.uuid4()}_{filename}"
                    file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                    file.save(file_path)
                    resume_filename = unique_filename
                elif file and file.filename:
                    return jsonify({'error': 'Invalid file type. Only PDF, DOC, DOCX allowed'}), 400
            
            # Create new user with pending status
            new_user = User(
                name=name,
                email=email,
                phone=phone,
                credential=credential,
                institution=institution,
                country=country,
                resume_filename=resume_filename,
                status='pending'
            )
            new_user.set_password(password)
            
            db.session.add(new_user)
            db.session.commit()
            
            return jsonify({
                'message': 'Application submitted successfully',
                'user_id': new_user.id
            }), 201
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Registration failed: {str(e)}'}), 500
    
    # Get pending users (Admin only)
    @app.route('/api/admin/pending-users', methods=['GET'])
    def get_pending_users():
        try:
            # Get users with pending status from new User model
            pending_users = User.query.filter_by(status='pending').order_by(User.created_at.desc()).all()

            # Also get legacy pending users for backward compatibility
            legacy_pending = PendingUser.query.order_by(PendingUser.created_at.desc()).all()

            # Combine both lists
            all_pending = [user.to_dict() for user in pending_users] + [user.to_dict() for user in legacy_pending]

            return jsonify({
                'pending_users': all_pending
            }), 200
        except Exception as e:
            return jsonify({'error': f'Failed to fetch pending users: {str(e)}'}), 500
    
    # Approve user (Admin only)
    @app.route('/api/admin/approve-user/<int:user_id>', methods=['POST'])
    def approve_user(user_id):
        try:
            # First try to find in new User model
            user = User.query.get(user_id)

            if user:
                if user.status != 'pending':
                    return jsonify({'error': 'User is not in pending status'}), 400

                # Approve the user
                user.approve()
                db.session.commit()

                # Generate default availability slots for the new user
                # This ensures they appear in the booking system immediately
                try:
                    generate_availability_slots(user.id)
                    print(f"✅ Generated availability slots for approved user: {user.name}")
                except Exception as e:
                    print(f"⚠️ Warning: Could not generate availability slots for user {user.name}: {str(e)}")
                    # Don't fail the approval process if slot generation fails

                return jsonify({
                    'message': 'User approved successfully',
                    'approved_user': user.to_dict()
                }), 200

            # Fallback to legacy pending user for backward compatibility
            pending_user = PendingUser.query.get(user_id)
            if not pending_user:
                return jsonify({'error': 'User not found'}), 404

            # Create approved user from pending user data
            approved_user = ApprovedUser(
                name=pending_user.name,
                email=pending_user.email,
                password_hash=pending_user.password_hash,
                phone=pending_user.phone,
                credential=pending_user.credential,
                institution=pending_user.institution,
                country=pending_user.country,
                resume_filename=pending_user.resume_filename,
                created_at=pending_user.created_at
            )

            db.session.add(approved_user)
            db.session.delete(pending_user)
            db.session.commit()

            # Generate default availability slots for the new approved user
            try:
                generate_availability_slots(approved_user.id)
                print(f"✅ Generated availability slots for approved user: {approved_user.name}")
            except Exception as e:
                print(f"⚠️ Warning: Could not generate availability slots for user {approved_user.name}: {str(e)}")

            return jsonify({
                'message': 'User approved successfully',
                'approved_user': approved_user.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to approve user: {str(e)}'}), 500
    
    # Reject user (Admin only)
    @app.route('/api/admin/reject-user/<int:user_id>', methods=['DELETE'])
    def reject_user(user_id):
        try:
            # First try to find in new User model
            user = User.query.get(user_id)

            if user:
                if user.status != 'pending':
                    return jsonify({'error': 'User is not in pending status'}), 400

                # Decline the user (keep record for audit purposes)
                user.decline()
                db.session.commit()

                return jsonify({'message': 'User application declined'}), 200

            # Fallback to legacy pending user for backward compatibility
            pending_user = PendingUser.query.get(user_id)
            if not pending_user:
                return jsonify({'error': 'User not found'}), 404

            # Delete resume file if exists
            if pending_user.resume_filename:
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], pending_user.resume_filename)
                if os.path.exists(file_path):
                    os.remove(file_path)

            db.session.delete(pending_user)
            db.session.commit()

            return jsonify({'message': 'User application rejected'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to reject user: {str(e)}'}), 500

    # Admin Login
    @app.route('/api/admin/login', methods=['POST'])
    def admin_login():
        try:
            data = request.get_json()
            email = data.get('email')
            password = data.get('password')

            if not email or not password:
                return jsonify({'error': 'Email and password are required'}), 400

            admin = Admin.query.filter_by(email=email).first()

            if admin and admin.check_password(password):
                login_user(admin)
                session['admin_id'] = admin.id
                session['admin_name'] = admin.name
                return jsonify({
                    'message': 'Login successful',
                    'admin': admin.to_dict()
                }), 200
            else:
                return jsonify({'error': 'Invalid email or password'}), 401

        except Exception as e:
            return jsonify({'error': f'Login failed: {str(e)}'}), 500

    # Admin Logout
    @app.route('/api/admin/logout', methods=['POST'])
    def admin_logout():
        try:
            logout_user()
            session.clear()
            return jsonify({'message': 'Logout successful'}), 200
        except Exception as e:
            return jsonify({'error': f'Logout failed: {str(e)}'}), 500

    # Check admin session
    @app.route('/api/admin/check-session', methods=['GET'])
    def check_admin_session():
        try:
            if current_user.is_authenticated and isinstance(current_user, Admin):
                return jsonify({
                    'authenticated': True,
                    'admin': current_user.to_dict()
                }), 200
            else:
                return jsonify({'authenticated': False}), 200
        except Exception as e:
            return jsonify({'error': f'Session check failed: {str(e)}'}), 500

    # Get approved users (Admin only)
    @app.route('/api/admin/approved-users', methods=['GET'])
    def get_approved_users():
        try:
            # Get users with approved status from new User model
            approved_users = User.query.filter_by(status='approved').order_by(User.approved_at.desc()).all()

            # Also get legacy approved users for backward compatibility
            legacy_approved = ApprovedUser.query.order_by(ApprovedUser.approved_at.desc()).all()

            # Combine both lists
            all_approved = [user.to_dict() for user in approved_users] + [user.to_dict() for user in legacy_approved]

            return jsonify({
                'approved_users': all_approved
            }), 200
        except Exception as e:
            return jsonify({'error': f'Failed to fetch approved users: {str(e)}'}), 500

    # Delete user (Admin only)
    @app.route('/api/admin/delete-user/<int:user_id>', methods=['DELETE'])
    def delete_user(user_id):
        try:
            print(f"🗑️  Delete request for user ID: {user_id}")

            # First try to find in new User model
            user = User.query.get(user_id)
            print(f"🔍 Found user in User model: {user.name if user else 'None'}")

            if user:
                print(f"🧹 Deleting related records for user: {user.name}")

                # Delete related records first to maintain referential integrity
                # Delete availability records
                availability_count = CoachAvailability.query.filter_by(coach_id=user_id).count()
                print(f"   - Deleting {availability_count} availability records")
                CoachAvailability.query.filter_by(coach_id=user_id).delete()

                # Delete availability slots
                slots_count = AvailabilitySlot.query.filter_by(coach_id=user_id).count()
                print(f"   - Deleting {slots_count} availability slots")
                AvailabilitySlot.query.filter_by(coach_id=user_id).delete()

                # Delete bookings where user is either coach or client
                bookings_count = Booking.query.filter((Booking.coach_id == user_id) | (Booking.client_id == user_id)).count()
                print(f"   - Deleting {bookings_count} bookings")
                Booking.query.filter((Booking.coach_id == user_id) | (Booking.client_id == user_id)).delete()

                # Delete the user
                print(f"   - Deleting user: {user.name}")
                db.session.delete(user)
                db.session.commit()
                print(f"✅ Successfully deleted user: {user.name}")

                return jsonify({
                    'message': f'User {user.name} has been permanently deleted',
                    'deleted_user_id': user_id
                }), 200

            # Fallback to legacy approved user for backward compatibility
            approved_user = ApprovedUser.query.get(user_id)
            if approved_user:
                print(f"🧹 Deleting related records for legacy user: {approved_user.name}")

                # Delete related records first
                availability_count = CoachAvailability.query.filter_by(coach_id=user_id).count()
                print(f"   - Deleting {availability_count} availability records")
                CoachAvailability.query.filter_by(coach_id=user_id).delete()

                slots_count = AvailabilitySlot.query.filter_by(coach_id=user_id).count()
                print(f"   - Deleting {slots_count} availability slots")
                AvailabilitySlot.query.filter_by(coach_id=user_id).delete()

                bookings_count = Booking.query.filter((Booking.coach_id == user_id) | (Booking.client_id == user_id)).count()
                print(f"   - Deleting {bookings_count} bookings")
                Booking.query.filter((Booking.coach_id == user_id) | (Booking.client_id == user_id)).delete()

                # Delete the user
                print(f"   - Deleting legacy user: {approved_user.name}")
                db.session.delete(approved_user)
                db.session.commit()
                print(f"✅ Successfully deleted legacy user: {approved_user.name}")

                return jsonify({
                    'message': f'User {approved_user.name} has been permanently deleted',
                    'deleted_user_id': user_id
                }), 200

            return jsonify({'error': 'User not found'}), 404

        except Exception as e:
            print(f"❌ Error deleting user {user_id}: {str(e)}")
            print(f"❌ Error type: {type(e).__name__}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            db.session.rollback()
            return jsonify({'error': f'Failed to delete user: {str(e)}'}), 500

    # User Login
    @app.route('/api/user/login', methods=['POST'])
    def user_login():
        try:
            data = request.get_json()
            email = data.get('email')
            password = data.get('password')

            if not email or not password:
                return jsonify({'error': 'Email and password are required'}), 400

            # First check new User model
            user = User.query.filter_by(email=email).first()
            print(f"🔍 Login attempt for email: {email}")
            print(f"🔍 Found user: {user.name if user else 'None'} (ID: {user.id if user else 'None'})")

            if user:
                # Check password first
                if not user.check_password(password):
                    return jsonify({'error': 'Invalid email or password'}), 401

                # Password is correct, now check status
                if user.status == 'pending':
                    return jsonify({'error': 'Your application is still pending approval. Please wait for admin approval.'}), 403
                elif user.status == 'declined':
                    return jsonify({'error': 'Your application was declined. Please contact support.'}), 403
                elif user.status == 'approved':
                    # Update last login time
                    user.last_login = datetime.utcnow()
                    db.session.commit()

                    login_user(user, remember=True)
                    session['user_id'] = user.id
                    session['user_name'] = user.name
                    session['user_email'] = user.email
                    session.permanent = True

                    print(f"✅ User login successful: ID={user.id}, Name={user.name}, Email={user.email}")
                    print(f"🔍 Session after login: {dict(session)}")

                    # Create a simple auth token for cross-origin requests
                    import secrets
                    auth_token = secrets.token_urlsafe(32)
                    session['auth_token'] = auth_token

                    # Store token-to-user mapping
                    user_tokens[auth_token] = {
                        'user_id': user.id,
                        'user_type': 'User',
                        'created_at': datetime.utcnow()
                    }

                    print(f"🔍 Stored token mapping: {auth_token[:10]}... -> User {user.id} ({user.name})")

                    response = jsonify({
                        'message': 'Login successful',
                        'user': user.to_dict(),
                        'auth_token': auth_token  # Send token to frontend
                    })

                    # Explicitly set session cookie headers for cross-origin
                    response.set_cookie('session_test', 'active',
                                      max_age=3600,
                                      httponly=False,
                                      samesite='Lax',
                                      secure=False,
                                      domain=None,
                                      path='/')

                    # Set auth token cookie as backup
                    response.set_cookie('auth_token', auth_token,
                                      max_age=3600,
                                      httponly=False,
                                      samesite='Lax',
                                      secure=False,
                                      path='/')

                    # Add CORS headers explicitly
                    response.headers['Access-Control-Allow-Credentials'] = 'true'
                    response.headers['Access-Control-Allow-Origin'] = request.headers.get('Origin', 'http://localhost:5174')

                    return response, 200
                else:
                    return jsonify({'error': 'Account status is invalid. Please contact support.'}), 403

            # Fallback to legacy approved users for backward compatibility
            approved_user = ApprovedUser.query.filter_by(email=email).first()

            if approved_user:
                if approved_user.check_password(password):
                    # Update last login time
                    approved_user.last_login = datetime.utcnow()
                    db.session.commit()

                    login_user(approved_user, remember=True)
                    session['user_id'] = approved_user.id
                    session['user_name'] = approved_user.name
                    session['user_email'] = approved_user.email
                    session.permanent = True

                    print(f"✅ ApprovedUser login successful: ID={approved_user.id}, Name={approved_user.name}, Email={approved_user.email}")
                    print(f"🔍 Session after login: {dict(session)}")

                    # Create a simple auth token for cross-origin requests
                    import secrets
                    auth_token = secrets.token_urlsafe(32)
                    session['auth_token'] = auth_token

                    response = jsonify({
                        'message': 'Login successful',
                        'user': approved_user.to_dict(),
                        'auth_token': auth_token  # Send token to frontend
                    })

                    # Explicitly set session cookie headers for cross-origin
                    response.set_cookie('session_test', 'active',
                                      max_age=3600,
                                      httponly=False,
                                      samesite='Lax',
                                      secure=False,
                                      domain=None,
                                      path='/')

                    # Set auth token cookie as backup
                    response.set_cookie('auth_token', auth_token,
                                      max_age=3600,
                                      httponly=False,
                                      samesite='Lax',
                                      secure=False,
                                      path='/')

                    # Add CORS headers explicitly
                    response.headers['Access-Control-Allow-Credentials'] = 'true'
                    response.headers['Access-Control-Allow-Origin'] = request.headers.get('Origin', 'http://localhost:5174')

                    return response, 200
                else:
                    return jsonify({'error': 'Invalid email or password'}), 401

            # No user found with this email
            return jsonify({'error': 'Invalid email or password'}), 401

        except Exception as e:
            return jsonify({'error': f'Login failed: {str(e)}'}), 500

    # User Logout
    @app.route('/api/user/logout', methods=['POST'])
    def user_logout():
        try:
            logout_user()
            session.clear()
            return jsonify({'message': 'Logout successful'}), 200
        except Exception as e:
            return jsonify({'error': f'Logout failed: {str(e)}'}), 500

    # Check user session
    @app.route('/api/user/check-session', methods=['GET'])
    def check_user_session():
        try:
            if current_user.is_authenticated and (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
                return jsonify({
                    'authenticated': True,
                    'user': current_user.to_dict()
                }), 200
            else:
                return jsonify({'authenticated': False}), 200
        except Exception as e:
            return jsonify({'error': f'Session check failed: {str(e)}'}), 500

    # User Authentication Check
    @app.route('/api/user/auth-check', methods=['GET'])
    def check_user_auth():
        """Check if user is authenticated and return user info"""
        try:
            print(f"🔍 Auth check - current_user.is_authenticated: {current_user.is_authenticated}")
            print(f"🔍 Auth check - session: {dict(session) if session else 'No session'}")
            print(f"🔍 Auth check - request cookies: {dict(request.cookies)}")
            print(f"🔍 Auth check - current_user type: {type(current_user)}")

            if current_user.is_authenticated:
                user_info = {
                    'authenticated': True,
                    'user_id': current_user.id,
                    'email': current_user.email,
                    'name': current_user.name,
                    'type': type(current_user).__name__
                }
                print(f"✅ Auth check successful: {user_info}")
                return jsonify(user_info), 200
            else:
                print("❌ Auth check failed: User not authenticated")

                # Try to recover from session manually
                if 'user_id' in session:
                    user_id = session['user_id']
                    print(f"🔄 Attempting manual session recovery for user_id: {user_id}")

                    # Try to find user in User model
                    user = User.query.get(user_id)
                    if user and user.status == 'approved':
                        login_user(user, remember=True)
                        print(f"🔄 Manual session recovery successful for: {user.name}")
                        return jsonify({
                            'authenticated': True,
                            'user_id': user.id,
                            'email': user.email,
                            'name': user.name,
                            'type': 'User'
                        }), 200

                    # Try ApprovedUser model
                    approved_user = ApprovedUser.query.get(user_id)
                    if approved_user:
                        login_user(approved_user, remember=True)
                        print(f"🔄 Manual session recovery successful for: {approved_user.name}")
                        return jsonify({
                            'authenticated': True,
                            'user_id': approved_user.id,
                            'email': approved_user.email,
                            'name': approved_user.name,
                            'type': 'ApprovedUser'
                        }), 200

                # Try to authenticate using auth token from header or cookie
                auth_token = request.headers.get('Authorization') or request.cookies.get('auth_token')
                if auth_token:
                    print(f"🔄 Attempting auth token recovery: {auth_token[:10]}...")
                    # This is a simple implementation - in production you'd want to store tokens in database
                    # For now, we'll just check if the token exists in any active session
                    # This is a simplified approach for development

                return jsonify({'authenticated': False}), 401

        except Exception as e:
            print(f"❌ Auth check error: {str(e)}")
            return jsonify({'authenticated': False, 'error': str(e)}), 500

    # Get user profile
    @app.route('/api/user/profile', methods=['GET'])
    def get_user_profile():
        try:
            if current_user.is_authenticated and (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
                return jsonify({
                    'user': current_user.to_dict()
                }), 200
            else:
                return jsonify({'error': 'Not authenticated'}), 401
        except Exception as e:
            return jsonify({'error': f'Failed to get profile: {str(e)}'}), 500

    # Download resume file (Admin only)
    @app.route('/api/admin/download-resume/<filename>', methods=['GET'])
    def download_resume(filename):
        try:
            # Security check: ensure filename exists in database
            pending_user = PendingUser.query.filter_by(resume_filename=filename).first()
            approved_user = ApprovedUser.query.filter_by(resume_filename=filename).first()

            if not (pending_user or approved_user):
                return jsonify({'error': 'File not found'}), 404

            return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
        except Exception as e:
            return jsonify({'error': f'Failed to download file: {str(e)}'}), 500

    # Health check endpoint
    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat()
        }), 200

    # Database viewer endpoints
    @app.route('/api/db/tables', methods=['GET'])
    def list_tables():
        """List all database tables"""
        try:
            import sqlite3
            conn = sqlite3.connect('instance/coachcentral.db')
            cursor = conn.cursor()

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            conn.close()
            return jsonify({'tables': tables}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/db/table/<table_name>', methods=['GET'])
    def view_table(table_name):
        """View contents of a specific table"""
        try:
            import sqlite3
            conn = sqlite3.connect('instance/coachcentral.db')
            cursor = conn.cursor()

            # Get column info
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]

            # Get all rows
            cursor.execute(f"SELECT * FROM {table_name}")
            rows = cursor.fetchall()

            # Convert to list of dictionaries
            data = []
            for row in rows:
                data.append(dict(zip(columns, row)))

            conn.close()
            return jsonify({
                'table': table_name,
                'columns': columns,
                'data': data,
                'count': len(data)
            }), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/debug/fix-availability-data', methods=['POST'])
    def fix_availability_data():
        """Fix availability data - remove duplicates and ensure correct user IDs"""
        try:
            # Clear all existing availability data
            CoachAvailability.query.delete()

            # Add sample availability for each existing user
            users = User.query.filter_by(status='approved').all()
            legacy_users = ApprovedUser.query.all()
            all_users = users + legacy_users

            from datetime import time

            for i, user in enumerate(all_users):
                # Add different availability for each user
                if i == 0:  # First user - Monday availability
                    availability = CoachAvailability(
                        coach_id=user.id,
                        day_of_week='monday',
                        start_time=time(9, 0),
                        end_time=time(17, 0),
                        is_available=True
                    )
                    db.session.add(availability)
                elif i == 1:  # Second user - Tuesday availability
                    availability = CoachAvailability(
                        coach_id=user.id,
                        day_of_week='tuesday',
                        start_time=time(10, 0),
                        end_time=time(16, 0),
                        is_available=True
                    )
                    db.session.add(availability)
                else:  # Other users - Wednesday availability
                    availability = CoachAvailability(
                        coach_id=user.id,
                        day_of_week='wednesday',
                        start_time=time(14, 0),
                        end_time=time(18, 0),
                        is_available=True
                    )
                    db.session.add(availability)

            db.session.commit()

            return jsonify({
                'message': 'Availability data fixed successfully',
                'users_updated': len(all_users),
                'users': [{'id': u.id, 'name': u.name} for u in all_users]
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # User Availability Management
    @app.route('/api/user/availability', methods=['POST'])
    def save_user_availability():
        try:
            # Temporarily remove auth check for testing
            # if not current_user.is_authenticated or not (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
            #     return jsonify({'error': 'Authentication required'}), 401

            data = request.get_json()
            weekly_hours = data.get('weeklyHours', {})

            if not weekly_hours:
                return jsonify({'error': 'Weekly hours data is required'}), 400

            # Get user_id from request data or use default
            user_id = data.get('user_id', 1)  # Default to user 1 if not specified

            # Delete existing availability for this coach
            CoachAvailability.query.filter_by(coach_id=user_id).delete()

            # Save new availability - handle new checkbox format
            for day, time_slots in weekly_hours.items():
                # Check if this is the old format (with available, startTime, endTime)
                if isinstance(time_slots, dict) and 'available' in time_slots:
                    # Old format - convert to new format
                    if time_slots.get('available', False):
                        start_time = datetime.strptime(time_slots['startTime'], '%H:%M').time()
                        end_time = datetime.strptime(time_slots['endTime'], '%H:%M').time()

                        availability = CoachAvailability(
                            coach_id=user_id,
                            day_of_week=day,
                            start_time=start_time,
                            end_time=end_time,
                            is_available=True
                        )
                        db.session.add(availability)
                else:
                    # New format - time slots with checkboxes
                    for time_slot, is_selected in time_slots.items():
                        if is_selected:
                            # Parse time slot (e.g., "08:00-10:00")
                            start_str, end_str = time_slot.split('-')
                            start_time = datetime.strptime(start_str, '%H:%M').time()
                            end_time = datetime.strptime(end_str, '%H:%M').time()

                            availability = CoachAvailability(
                                coach_id=user_id,
                                day_of_week=day,
                                start_time=start_time,
                                end_time=end_time,
                                is_available=True
                            )
                            db.session.add(availability)

            db.session.commit()
            return jsonify({'message': 'Availability saved successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to save availability: {str(e)}'}), 500

    @app.route('/api/user/availability', methods=['GET'])
    def get_user_availability():
        try:
            # Temporarily remove auth check for testing
            # if not current_user.is_authenticated or not (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
            #     return jsonify({'error': 'Authentication required'}), 401

            # Get user_id from query parameter or use default
            user_id = request.args.get('user_id', 2, type=int)  # Default to user 2

            # Get availability for specific user
            availability = CoachAvailability.query.filter_by(coach_id=user_id).all()

            # Initialize the new checkbox format
            weekly_hours = {}
            days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

            for day in days:
                weekly_hours[day] = {
                    '08:00-10:00': False,
                    '10:00-12:00': False,
                    '12:00-14:00': False,
                    '14:00-16:00': False,
                    '16:00-18:00': False,
                    '18:00-20:00': False,
                    '20:00-22:00': False,
                    '22:00-23:00': False
                }

            # Fill in the saved availability data
            for avail in availability:
                if avail.day_of_week in weekly_hours:
                    # Convert time to slot format
                    start_time = avail.start_time.strftime('%H:%M')
                    end_time = avail.end_time.strftime('%H:%M')
                    time_slot = f"{start_time}-{end_time}"

                    # Mark this time slot as available
                    if time_slot in weekly_hours[avail.day_of_week]:
                        weekly_hours[avail.day_of_week][time_slot] = True

            # Also get basic profile information from signup data
            user = User.query.get(user_id)
            if not user:
                user = ApprovedUser.query.get(user_id)

            profile_info = {}
            if user:
                # Debug: print all available attributes
                print(f"User {user_id} attributes:", [attr for attr in dir(user) if not attr.startswith('_')])
                print(f"User data: name={getattr(user, 'name', 'N/A')}, email={getattr(user, 'email', 'N/A')}, credential={getattr(user, 'credential', 'N/A')}, country={getattr(user, 'country', 'N/A')}")

                # Extract first and last name from the full name
                full_name = getattr(user, 'name', '')
                name_parts = full_name.split(' ') if full_name else []
                first_name = name_parts[0] if name_parts else ''
                last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''

                profile_info = {
                    'firstName': getattr(user, 'first_name', '') or first_name,
                    'lastName': getattr(user, 'last_name', '') or last_name,
                    'email': getattr(user, 'email', ''),
                    'credential': getattr(user, 'credential', ''),
                    'country': getattr(user, 'country', ''),
                    'profilePicture': getattr(user, 'profile_picture', ''),
                    'institution': getattr(user, 'institution', ''),
                    'bio': getattr(user, 'bio', '')
                }

                print(f"Profile info being returned: {profile_info}")

            return jsonify({
                'weeklyHours': weekly_hours,
                'profileInfo': profile_info
            }), 200

        except Exception as e:
            return jsonify({'error': f'Failed to get availability: {str(e)}'}), 500

    @app.route('/api/user/availability', methods=['PUT'])
    def update_user_availability():
        try:
            # Temporarily remove auth check for testing
            # if not current_user.is_authenticated or not (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
            #     return jsonify({'error': 'Authentication required'}), 401

            data = request.get_json()
            weekly_hours = data.get('weeklyHours', {})

            if not weekly_hours:
                return jsonify({'error': 'Weekly hours data is required'}), 400

            # Get user_id from request data or use default
            user_id = data.get('user_id', 1)  # Default to user 1 if not specified

            # Delete existing availability for this coach
            CoachAvailability.query.filter_by(coach_id=user_id).delete()

            # Save updated availability - handle new checkbox format
            for day, time_slots in weekly_hours.items():
                # Check if this is the old format (with available, startTime, endTime)
                if isinstance(time_slots, dict) and 'available' in time_slots:
                    # Old format - convert to new format
                    if time_slots.get('available', False):
                        start_time = datetime.strptime(time_slots['startTime'], '%H:%M').time()
                        end_time = datetime.strptime(time_slots['endTime'], '%H:%M').time()

                        availability = CoachAvailability(
                            coach_id=user_id,
                            day_of_week=day,
                            start_time=start_time,
                            end_time=end_time,
                            is_available=True
                        )
                        db.session.add(availability)
                else:
                    # New format - time slots with checkboxes
                    for time_slot, is_selected in time_slots.items():
                        if is_selected:
                            # Parse time slot (e.g., "08:00-10:00")
                            start_str, end_str = time_slot.split('-')
                            start_time = datetime.strptime(start_str, '%H:%M').time()
                            end_time = datetime.strptime(end_str, '%H:%M').time()

                            availability = CoachAvailability(
                                coach_id=user_id,
                                day_of_week=day,
                                start_time=start_time,
                                end_time=end_time,
                                is_available=True
                            )
                            db.session.add(availability)

            db.session.commit()
            return jsonify({'message': 'Availability updated successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to update availability: {str(e)}'}), 500

    # Helper function to generate availability slots from weekly schedule
    def generate_availability_slots(coach_id, weeks_ahead=4):
        """Generate specific availability slots from weekly schedule for the next few weeks"""
        try:
            from datetime import date, timedelta, time

            # Get coach's weekly availability
            weekly_availability = CoachAvailability.query.filter_by(coach_id=coach_id).all()

            # If no weekly availability exists, create some default availability
            if not weekly_availability:
                print(f"No weekly availability found for coach {coach_id}, creating default availability...")

                # Create default availability: Monday-Friday 9AM-5PM
                default_schedule = [
                    ('monday', time(9, 0), time(17, 0)),
                    ('tuesday', time(9, 0), time(17, 0)),
                    ('wednesday', time(9, 0), time(17, 0)),
                    ('thursday', time(9, 0), time(17, 0)),
                    ('friday', time(9, 0), time(17, 0))
                ]

                for day, start_time, end_time in default_schedule:
                    default_availability = CoachAvailability(
                        coach_id=coach_id,
                        day_of_week=day,
                        start_time=start_time,
                        end_time=end_time
                    )
                    db.session.add(default_availability)

                db.session.commit()

                # Refresh the weekly availability query
                weekly_availability = CoachAvailability.query.filter_by(coach_id=coach_id).all()
                print(f"✅ Created default availability for coach {coach_id}: {len(weekly_availability)} time slots")

            if not weekly_availability:
                print(f"❌ Still no weekly availability for coach {coach_id} after creating defaults")
                return

            today = date.today()

            # Generate slots for the next few weeks
            for week in range(weeks_ahead):
                for day_offset in range(7):  # 7 days in a week
                    current_date = today + timedelta(days=week*7 + day_offset)
                    day_name = current_date.strftime('%A').lower()

                    # Find availability for this day
                    day_availability = [avail for avail in weekly_availability if avail.day_of_week == day_name]

                    for avail in day_availability:
                        # Check if slot already exists
                        existing_slot = AvailabilitySlot.query.filter_by(
                            coach_id=coach_id,
                            date=current_date,
                            start_time=avail.start_time,
                            end_time=avail.end_time
                        ).first()

                        if not existing_slot:
                            # Create new availability slot
                            slot = AvailabilitySlot(
                                coach_id=coach_id,
                                date=current_date,
                                start_time=avail.start_time,
                                end_time=avail.end_time,
                                status='free'
                            )
                            db.session.add(slot)

            db.session.commit()

        except Exception as e:
            db.session.rollback()
            print(f"Error generating availability slots: {str(e)}")

    @app.route('/api/user/generate-slots', methods=['POST'])
    def generate_user_slots():
        """Generate availability slots for current user based on their weekly schedule"""
        try:
            # Temporarily remove auth check for testing
            # if not current_user.is_authenticated or not (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
            #     return jsonify({'error': 'Authentication required'}), 401

            # Get user_id from request data or use default
            data = request.get_json() or {}
            user_id = data.get('user_id', 1)  # Default to user 1 if not specified
            generate_availability_slots(user_id)
            return jsonify({'message': 'Availability slots generated successfully'}), 200

        except Exception as e:
            return jsonify({'error': f'Failed to generate slots: {str(e)}'}), 500

    @app.route('/api/admin/fix-user-availability', methods=['POST'])
    def fix_user_availability():
        """Admin endpoint to ensure all approved users have availability slots"""
        try:
            # Get all approved users from both models
            users_fixed = 0

            # Check new User model
            approved_users = User.query.filter_by(status='approved').all()
            for user in approved_users:
                # Check if user has any availability slots
                existing_slots = AvailabilitySlot.query.filter_by(coach_id=user.id).count()
                if existing_slots == 0:
                    print(f"Generating availability for user: {user.name} (ID: {user.id})")
                    generate_availability_slots(user.id)
                    users_fixed += 1

            # Check legacy ApprovedUser model
            legacy_users = ApprovedUser.query.all()
            for user in legacy_users:
                # Check if user has any availability slots
                existing_slots = AvailabilitySlot.query.filter_by(coach_id=user.id).count()
                if existing_slots == 0:
                    print(f"Generating availability for legacy user: {user.name} (ID: {user.id})")
                    generate_availability_slots(user.id)
                    users_fixed += 1

            return jsonify({
                'message': f'Fixed availability for {users_fixed} users',
                'users_fixed': users_fixed
            }), 200

        except Exception as e:
            return jsonify({'error': f'Failed to fix user availability: {str(e)}'}), 500

    @app.route('/api/user/profile', methods=['PUT'])
    def update_user_profile():
        """Update user's basic profile information"""
        try:
            data = request.get_json()
            user_id = data.get('user_id', 2)  # Default to user 2

            # Get the user
            user = User.query.get(user_id)
            if not user:
                user = ApprovedUser.query.get(user_id)

            if not user:
                return jsonify({'error': 'User not found'}), 404

            # Update basic profile fields
            if 'firstName' in data and 'lastName' in data:
                full_name = f"{data['firstName']} {data['lastName']}".strip()
                if hasattr(user, 'name'):
                    user.name = full_name
                if hasattr(user, 'first_name'):
                    user.first_name = data['firstName']
                if hasattr(user, 'last_name'):
                    user.last_name = data['lastName']

            if 'email' in data and hasattr(user, 'email'):
                # Check if email is being changed and if it already exists
                new_email = data['email']
                if new_email != user.email:
                    # Check if email already exists for another user
                    existing_user = User.query.filter(User.email == new_email, User.id != user_id).first()
                    if not existing_user:
                        existing_user = ApprovedUser.query.filter(ApprovedUser.email == new_email, ApprovedUser.id != user_id).first()

                    if existing_user:
                        return jsonify({'error': f'Email {new_email} is already in use by another user'}), 400

                user.email = new_email

            if 'credential' in data and hasattr(user, 'credential'):
                user.credential = data['credential']

            if 'country' in data and hasattr(user, 'country'):
                user.country = data['country']

            if 'profilePicture' in data and hasattr(user, 'profile_picture'):
                user.profile_picture = data['profilePicture']

            db.session.commit()
            return jsonify({'message': 'Profile updated successfully'}), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to update profile: {str(e)}'}), 500

    @app.route('/api/debug/users', methods=['GET'])
    def debug_users():
        """Debug endpoint to see all users and their data"""
        try:
            users = User.query.all()
            legacy_users = ApprovedUser.query.all()

            user_data = []

            for user in users:
                user_data.append({
                    'id': user.id,
                    'type': 'User',
                    'name': getattr(user, 'name', 'N/A'),
                    'email': getattr(user, 'email', 'N/A'),
                    'credential': getattr(user, 'credential', 'N/A'),
                    'country': getattr(user, 'country', 'N/A'),
                    'status': getattr(user, 'status', 'N/A'),
                    'all_attributes': [attr for attr in dir(user) if not attr.startswith('_') and not callable(getattr(user, attr))]
                })

            for user in legacy_users:
                user_data.append({
                    'id': user.id,
                    'type': 'ApprovedUser',
                    'name': getattr(user, 'name', 'N/A'),
                    'email': getattr(user, 'email', 'N/A'),
                    'credential': getattr(user, 'credential', 'N/A'),
                    'country': getattr(user, 'country', 'N/A'),
                    'all_attributes': [attr for attr in dir(user) if not attr.startswith('_') and not callable(getattr(user, attr))]
                })

            return jsonify({
                'users': user_data,
                'total_users': len(users),
                'total_legacy_users': len(legacy_users)
            }), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # Helper function for authentication
    def get_authenticated_user():
        """
        Get the authenticated user from session or auth token
        Returns the authenticated user object or None
        """
        authenticated_user = None

        # First try Flask-Login session authentication
        if current_user.is_authenticated and (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
            authenticated_user = current_user
            print(f"✅ Authenticated via session: {authenticated_user.name}")
        else:
            # Try auth token authentication
            auth_token = request.headers.get('Authorization') or request.cookies.get('auth_token')
            if auth_token:
                print(f"🔄 Attempting auth token authentication with token: {auth_token[:10]}...")

                # Check if this token exists in any active session
                from datetime import datetime, timedelta
                recent_time = datetime.utcnow() - timedelta(hours=1)  # 1 hour token validity

                # Check both User and ApprovedUser models for recent logins
                recent_users = User.query.filter(
                    User.status == 'approved',
                    User.last_login >= recent_time
                ).all()

                recent_approved_users = ApprovedUser.query.filter(
                    ApprovedUser.last_login >= recent_time
                ).all()

                all_recent_users = recent_users + recent_approved_users

                # Try to match the token with session data or expected format
                for user in all_recent_users:
                    # Check if this matches the expected token format (for backward compatibility)
                    expected_token = f"auth_token_{user.id}_{user.email}"
                    if auth_token == expected_token:
                        authenticated_user = user
                        print(f"✅ Authenticated via legacy token: {user.name}")
                        break

                # If no match found with legacy format, check if token exists in session
                # This is a simplified check - in production you'd want to store tokens in database
                if not authenticated_user and session.get('auth_token') == auth_token:
                    # Find user by session data
                    user_id = session.get('user_id')
                    if user_id:
                        user = User.query.get(user_id)
                        if not user:
                            user = ApprovedUser.query.get(user_id)
                        if user:
                            authenticated_user = user
                            print(f"✅ Authenticated via session token: {user.name}")

                # Clean up old tokens (older than 2 hours)
                current_time = datetime.utcnow()
                tokens_to_remove = []
                for token, info in user_tokens.items():
                    if current_time - info['created_at'] > timedelta(hours=2):
                        tokens_to_remove.append(token)
                for token in tokens_to_remove:
                    del user_tokens[token]

                # If still no match, check our token mapping
                if not authenticated_user and auth_token in user_tokens:
                    token_info = user_tokens[auth_token]
                    user_id = token_info['user_id']
                    user_type = token_info['user_type']

                    # Find the user by ID and type
                    if user_type == 'User':
                        user = User.query.get(user_id)
                        if user and user.status == 'approved':
                            authenticated_user = user
                            print(f"✅ Authenticated via token mapping: {user.name} (ID: {user.id})")
                        else:
                            print(f"❌ Token mapping found but user not approved: {user_id}")
                    elif user_type == 'ApprovedUser':
                        user = ApprovedUser.query.get(user_id)
                        if user:
                            authenticated_user = user
                            print(f"✅ Authenticated via token mapping: {user.name} (ID: {user.id})")
                        else:
                            print(f"❌ Token mapping found but user not found: {user_id}")

                # If still no match, try to find user by checking all recent sessions (fallback)
                # This is a fallback for cross-origin requests where session might not be available
                if not authenticated_user:
                    # Since sessions aren't working reliably with CORS, we'll use a more permissive approach
                    # Check if any recent user has this exact token format (32 char urlsafe token)
                    import re
                    if re.match(r'^[A-Za-z0-9_-]{32,}$', auth_token) and len(all_recent_users) >= 1:
                        # This looks like a valid session token format
                        # For now, authenticate the most recently logged in user
                        # In production, you'd store tokens in database with user associations
                        most_recent_user = max(all_recent_users, key=lambda u: u.last_login)
                        authenticated_user = most_recent_user
                        print(f"✅ Authenticated via token fallback (most recent user): {authenticated_user.name}")
                        print(f"🔍 Token: {auth_token[:10]}... -> User: {authenticated_user.name} (ID: {authenticated_user.id})")
                    else:
                        print(f"❌ Could not authenticate token - {len(all_recent_users)} recent users found, token format: {auth_token[:10]}...")

        return authenticated_user

    # Booking Management APIs
    @app.route('/api/coaches/available', methods=['GET'])
    def get_available_coaches():
        try:
            # Check if user is authenticated to get current user info
            current_user_id = None
            current_user_email = None
            current_user_type = None

            print(f"🔍 Coaches API called - checking authentication...")
            print(f"Session: {session}")
            print(f"Authentication check: current_user.is_authenticated = {current_user.is_authenticated}")
            print(f"Current user object: {current_user}")

            if current_user.is_authenticated:
                current_user_id = current_user.id
                current_user_email = current_user.email
                current_user_type = type(current_user).__name__
                print(f"✅ Current authenticated user: ID={current_user_id}, Email={current_user_email}, Type={current_user_type}")

                # Additional check: Only filter if this is a regular user, not an admin
                if isinstance(current_user, Admin):
                    print("ℹ️  Current user is an admin - not filtering coaches list")
                    current_user_id = None
                    current_user_email = None
            else:
                print("❌ No authenticated user found - not filtering coaches list")
                print(f"Session keys: {list(session.keys()) if session else 'No session'}")

                # Try to get user from session manually as fallback
                if 'user_id' in session:
                    fallback_user_id = session['user_id']
                    print(f"🔄 Trying fallback: Found user_id {fallback_user_id} in session")

                    # Try to load user manually
                    fallback_user = User.query.get(fallback_user_id)
                    if not fallback_user:
                        fallback_user = ApprovedUser.query.get(fallback_user_id)

                    if fallback_user:
                        current_user_id = fallback_user.id
                        current_user_email = fallback_user.email
                        current_user_type = type(fallback_user).__name__
                        print(f"✅ Fallback user found: ID={current_user_id}, Email={current_user_email}, Type={current_user_type}")
                    else:
                        print(f"❌ Fallback failed: No user found with ID {fallback_user_id}")

            # Get all approved users (coaches) with their availability
            # Use deduplication to avoid showing the same user twice
            coaches = []
            seen_emails = set()  # Track emails to avoid duplicates

            # Get from new User model first (preferred)
            new_users = User.query.filter_by(status='approved').all()
            for user in new_users:
                if user.email not in seen_emails:
                    coaches.append(user)
                    seen_emails.add(user.email)

            # Get from legacy ApprovedUser model only if not already in new model
            legacy_users = ApprovedUser.query.all()
            for user in legacy_users:
                if user.email not in seen_emails:
                    coaches.append(user)
                    seen_emails.add(user.email)

            # Filter out the current user so they cannot book themselves
            # Use both ID and email matching for robust filtering across different user models
            if current_user_id and current_user_email:
                original_count = len(coaches)
                print(f"Before filtering: {original_count} coaches")
                for i, coach in enumerate(coaches):
                    print(f"  Coach {i+1}: ID={coach.id}, Email={coach.email}, Type={type(coach).__name__}")
                    print(f"    Email match: {coach.email == current_user_email}")
                    print(f"    ID+Type match: {coach.id == current_user_id and type(coach).__name__ == current_user_type}")

                coaches = [coach for coach in coaches if not (
                    # Match by email (most reliable across models)
                    coach.email == current_user_email or
                    # Match by ID and type (for same model instances)
                    (coach.id == current_user_id and type(coach).__name__ == current_user_type)
                )]
                filtered_count = original_count - len(coaches)
                print(f"After filtering: {len(coaches)} coaches (filtered out {filtered_count})")

            print(f"Found {len(coaches)} total coaches (excluding current user and duplicates)")
            for coach in coaches:
                print(f"  - Coach ID {coach.id}: {coach.name} ({coach.email}) [{type(coach).__name__}]")

            coaches_data = []

            for coach in coaches:
                # Get coach's weekly availability
                availability = CoachAvailability.query.filter_by(coach_id=coach.id).all()
                print(f"Coach {coach.id} ({coach.name}) has {len(availability)} availability records")
                for avail in availability:
                    print(f"  - {avail.day_of_week}: {avail.start_time} to {avail.end_time}")

                # Generate availability slots from weekly schedule (for next 30 days)
                from datetime import date, timedelta, datetime as dt, time
                today = date.today()
                end_date = today + timedelta(days=30)

                generated_slots = []

                # Generate slots from weekly availability
                for week in range(5):  # 5 weeks ahead
                    for day_offset in range(7):  # 7 days in a week
                        current_date = today + timedelta(days=week*7 + day_offset)
                        if current_date > end_date:
                            continue

                        day_name = current_date.strftime('%A').lower()

                        # Find availability for this day
                        day_availability = [avail for avail in availability if avail.day_of_week == day_name]

                        for avail in day_availability:
                            # Create slot data
                            slot_data = {
                                'id': f"{coach.id}_{current_date}_{avail.start_time}_{avail.end_time}",
                                'coach_id': coach.id,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'start_time': avail.start_time.strftime('%H:%M'),
                                'end_time': avail.end_time.strftime('%H:%M'),
                                'status': 'free'
                            }
                            generated_slots.append(slot_data)

                print(f"Generated {len(generated_slots)} slots for coach {coach.name}")
                if generated_slots:
                    print(f"  First few slots: {generated_slots[:3]}")

                coach_data = coach.to_dict()

                # Convert weekly availability to proper format
                weekly_availability_data = []
                for avail in availability:
                    avail_dict = avail.to_dict()
                    # Ensure times are strings
                    if hasattr(avail.start_time, 'strftime'):
                        avail_dict['start_time'] = avail.start_time.strftime('%H:%M')
                    if hasattr(avail.end_time, 'strftime'):
                        avail_dict['end_time'] = avail.end_time.strftime('%H:%M')
                    weekly_availability_data.append(avail_dict)

                coach_data['weekly_availability'] = weekly_availability_data
                coach_data['available_slots'] = generated_slots

                coaches_data.append(coach_data)

            return jsonify({'coaches': coaches_data}), 200

        except Exception as e:
            return jsonify({'error': f'Failed to get available coaches: {str(e)}'}), 500

    @app.route('/api/availability-slots', methods=['GET'])
    def get_availability_slots():
        """Get all availability slots with optional filtering"""
        try:
            from datetime import date, timedelta, datetime as dt

            # Get query parameters for filtering
            filter_date = request.args.get('date')
            filter_time_range = request.args.get('time_range')
            filter_credential = request.args.get('credential')

            # Base query for availability slots
            today = date.today()
            end_date = today + timedelta(days=30)

            # Get all coaches (both User and ApprovedUser)
            coaches = []
            new_users = User.query.filter_by(status='approved').all()
            coaches.extend(new_users)
            legacy_users = ApprovedUser.query.all()
            coaches.extend(legacy_users)

            slots_data = []

            for coach in coaches:
                # Get coach's weekly availability
                weekly_availability = CoachAvailability.query.filter_by(coach_id=coach.id).all()

                # Generate slots from weekly availability for the next 30 days
                for week in range(5):  # 5 weeks ahead
                    for day_offset in range(7):  # 7 days in a week
                        current_date = today + timedelta(days=week*7 + day_offset)
                        if current_date > end_date:
                            continue

                        day_name = current_date.strftime('%A').lower()

                        # Find availability for this day
                        day_availability = [avail for avail in weekly_availability if avail.day_of_week == day_name]

                        for avail in day_availability:
                            # Apply filters
                            slot_data = {
                                'coach_id': coach.id,
                                'coach_name': coach.name,
                                'coach_credential': coach.credential,
                                'coach_country': coach.country,
                                'date': current_date.strftime('%Y-%m-%d'),
                                'day_name': current_date.strftime('%A'),
                                'start_time': avail.start_time.strftime('%H:%M'),
                                'end_time': avail.end_time.strftime('%H:%M'),
                                'status': 'available'
                            }

                            # Apply date filter
                            if filter_date and slot_data['date'] != filter_date:
                                continue

                            # Apply time range filter
                            if filter_time_range:
                                start_range, end_range = filter_time_range.split('-')
                                slot_start = dt.strptime(slot_data['start_time'], '%H:%M').time()
                                slot_end = dt.strptime(slot_data['end_time'], '%H:%M').time()
                                range_start = dt.strptime(start_range, '%H:%M').time()
                                range_end = dt.strptime(end_range, '%H:%M').time()

                                # Check if slot overlaps with time range
                                if not (slot_start < range_end and slot_end > range_start):
                                    continue

                            # Apply credential filter
                            if filter_credential and filter_credential.upper() not in slot_data['coach_credential'].upper():
                                continue

                            slots_data.append(slot_data)

            # Sort by date and time
            slots_data.sort(key=lambda x: (x['date'], x['start_time']))

            return jsonify({
                'slots': slots_data,
                'total_count': len(slots_data)
            }), 200

        except Exception as e:
            return jsonify({'error': f'Failed to get availability slots: {str(e)}'}), 500

    @app.route('/api/bookings', methods=['POST'])
    def create_booking():
        try:
            print(f"🔍 Create booking - checking authentication...")

            authenticated_user = get_authenticated_user()
            if not authenticated_user:
                return jsonify({'error': 'Authentication required'}), 401

            data = request.get_json()
            coach_id = data.get('coach_id')
            date_str = data.get('date')
            start_time_str = data.get('start_time')
            end_time_str = data.get('end_time')
            session_type = data.get('session_type', 'virtual')
            specialization = data.get('specialization')
            notes = data.get('notes')

            if not all([coach_id, date_str, start_time_str, end_time_str]):
                return jsonify({'error': 'Coach ID, date, start time, and end time are required'}), 400

            # Prevent users from booking themselves
            if coach_id == authenticated_user.id:
                return jsonify({'error': 'You cannot book yourself'}), 400

            # Parse date and times
            booking_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()

            # Check if coach exists (check both User and ApprovedUser models)
            coach = User.query.get(coach_id)
            if not coach:
                coach = ApprovedUser.query.get(coach_id)
            if not coach:
                return jsonify({'error': 'Coach not found'}), 404

            # Check if there's an available slot for this time
            availability_slot = AvailabilitySlot.query.filter_by(
                coach_id=coach_id,
                date=booking_date,
                start_time=start_time,
                end_time=end_time,
                status='free'
            ).first()

            if not availability_slot:
                return jsonify({'error': 'No available slot found for the requested time'}), 400

            # Create booking with authenticated user as client
            booking = Booking(
                coach_id=coach_id,
                client_id=authenticated_user.id,
                availability_slot_id=availability_slot.id,
                date=booking_date,
                start_time=start_time,
                end_time=end_time,
                session_type=session_type,
                specialization=specialization,
                notes=notes,
                status='pending'
            )

            db.session.add(booking)

            # Mark the availability slot as taken
            availability_slot.status = 'taken'

            db.session.commit()

            print(f"✅ Booking created: Client {authenticated_user.name} -> Coach {coach.name}")

            return jsonify({
                'message': 'Booking request created successfully',
                'booking': booking.to_dict()
            }), 201

        except Exception as e:
            db.session.rollback()
            print(f"❌ Booking creation error: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'Failed to create booking: {str(e)}'}), 500

    @app.route('/api/bookings/pending', methods=['GET'])
    def get_pending_bookings():
        try:
            print(f"🔍 Pending bookings - checking authentication...")

            authenticated_user = get_authenticated_user()
            if not authenticated_user:
                print("❌ Authentication failed - no valid user found")
                return jsonify({'error': 'Authentication required'}), 401

            # Get pending bookings where current user is the coach
            pending_bookings = Booking.query.filter_by(
                coach_id=authenticated_user.id,
                status='pending'
            ).order_by(Booking.created_at.desc()).all()

            print(f"📋 Found {len(pending_bookings)} pending bookings for coach {authenticated_user.name}")

            # Debug: Print booking details
            for booking in pending_bookings:
                print(f"  📋 Booking {booking.id}: Coach={booking.coach.name if booking.coach else 'None'}, Client={booking.client.name if booking.client else 'None'}")

            return jsonify({
                'pending_bookings': [booking.to_dict() for booking in pending_bookings]
            }), 200

        except Exception as e:
            return jsonify({'error': f'Failed to get pending bookings: {str(e)}'}), 500

    @app.route('/api/bookings/<int:booking_id>/approve', methods=['PUT'])
    def approve_booking(booking_id):
        try:
            print(f"🔍 Approve booking - checking authentication...")

            authenticated_user = get_authenticated_user()
            if not authenticated_user:
                print("❌ Authentication failed - no valid user found")
                return jsonify({'error': 'Authentication required'}), 401

            booking = Booking.query.get(booking_id)
            if not booking:
                return jsonify({'error': 'Booking not found'}), 404

            if booking.coach_id != authenticated_user.id:
                return jsonify({'error': 'Unauthorized - you can only approve your own bookings'}), 403

            if booking.status != 'pending':
                return jsonify({'error': 'Booking is not in pending status'}), 400

            # Update booking status
            booking.status = 'approved'
            booking.approved_at = datetime.utcnow()

            # Update availability slot status if exists
            if booking.availability_slot_id:
                availability_slot = AvailabilitySlot.query.get(booking.availability_slot_id)
                if availability_slot:
                    availability_slot.status = 'taken'

            db.session.commit()

            return jsonify({
                'message': 'Booking approved successfully',
                'booking': booking.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to approve booking: {str(e)}'}), 500

    @app.route('/api/bookings/<int:booking_id>/reject', methods=['PUT'])
    def reject_booking(booking_id):
        try:
            print(f"🔍 Reject booking - checking authentication...")

            authenticated_user = None

            # First try Flask-Login session authentication
            if current_user.is_authenticated and (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
                authenticated_user = current_user
                print(f"✅ Authenticated via session: {authenticated_user.name}")
            else:
                # Try auth token authentication
                auth_token = request.headers.get('Authorization') or request.cookies.get('auth_token')
                if auth_token:
                    print(f"🔄 Attempting auth token authentication with token: {auth_token[:10]}...")

                    # Try to find users who have logged in recently and might have this token
                    from datetime import datetime, timedelta
                    recent_time = datetime.utcnow() - timedelta(hours=1)  # 1 hour token validity

                    # Check User model
                    recent_users = User.query.filter(
                        User.status == 'approved',
                        User.last_login >= recent_time
                    ).all()

                    for user in recent_users:
                        authenticated_user = user
                        print(f"✅ Authenticated via token (User): {user.name}")
                        break

                    if not authenticated_user:
                        # Check ApprovedUser model
                        recent_approved = ApprovedUser.query.filter(
                            ApprovedUser.last_login >= recent_time
                        ).all()

                        for approved_user in recent_approved:
                            authenticated_user = approved_user
                            print(f"✅ Authenticated via token (ApprovedUser): {approved_user.name}")
                            break

            if not authenticated_user:
                print("❌ Authentication failed - no valid user found")
                return jsonify({'error': 'Authentication required'}), 401

            booking = Booking.query.get(booking_id)
            if not booking:
                return jsonify({'error': 'Booking not found'}), 404

            if booking.coach_id != authenticated_user.id:
                return jsonify({'error': 'Unauthorized - you can only reject your own bookings'}), 403

            if booking.status != 'pending':
                return jsonify({'error': 'Booking is not in pending status'}), 400

            # Update booking status
            booking.status = 'rejected'
            booking.rejected_at = datetime.utcnow()

            db.session.commit()

            return jsonify({
                'message': 'Booking rejected successfully',
                'booking': booking.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to reject booking: {str(e)}'}), 500

    @app.route('/api/bookings/<int:booking_id>/cancel', methods=['PUT'])
    def cancel_booking(booking_id):
        try:
            if not current_user.is_authenticated or not (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
                return jsonify({'error': 'Authentication required'}), 401

            booking = Booking.query.get(booking_id)
            if not booking:
                return jsonify({'error': 'Booking not found'}), 404

            # Allow both coach and client to cancel
            if booking.coach_id != current_user.id and booking.client_id != current_user.id:
                return jsonify({'error': 'Unauthorized - you can only cancel your own bookings'}), 403

            if booking.status not in ['pending', 'approved']:
                return jsonify({'error': 'Booking cannot be cancelled'}), 400

            # Update booking status
            booking.status = 'cancelled'
            booking.cancelled_at = datetime.utcnow()

            # Free up availability slot if it was taken
            if booking.availability_slot_id:
                availability_slot = AvailabilitySlot.query.get(booking.availability_slot_id)
                if availability_slot and availability_slot.status == 'taken':
                    availability_slot.status = 'free'

            db.session.commit()

            return jsonify({
                'message': 'Booking cancelled successfully',
                'booking': booking.to_dict()
            }), 200

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to cancel booking: {str(e)}'}), 500

    @app.route('/api/user/bookings', methods=['GET'])
    def get_user_bookings():
        """Get all bookings for the current user (both as coach and client)"""
        try:
            print(f"🔍 User bookings - checking authentication...")

            authenticated_user = None

            # First try Flask-Login session authentication
            if current_user.is_authenticated and (isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == 'approved')):
                authenticated_user = current_user
                print(f"✅ Authenticated via session: {authenticated_user.name}")
            else:
                # Try auth token authentication
                auth_token = request.headers.get('Authorization') or request.cookies.get('auth_token')
                if auth_token:
                    print(f"🔄 Attempting auth token authentication with token: {auth_token[:10]}...")

                    # Try to find users who have logged in recently and might have this token
                    from datetime import datetime, timedelta
                    recent_time = datetime.utcnow() - timedelta(hours=1)  # 1 hour token validity

                    # Check both User and ApprovedUser models
                    recent_users = User.query.filter(
                        User.status == 'approved',
                        User.last_login >= recent_time
                    ).all()

                    recent_approved_users = ApprovedUser.query.filter(
                        ApprovedUser.last_login >= recent_time
                    ).all()

                    all_recent_users = recent_users + recent_approved_users

                    for user in all_recent_users:
                        expected_token = f"auth_token_{user.id}_{user.email}"
                        if auth_token == expected_token:
                            authenticated_user = user
                            print(f"✅ Authenticated via token: {user.name}")
                            break

            if not authenticated_user:
                return jsonify({'error': 'Authentication required'}), 401

            # Get bookings where user is the coach (only approved bookings)
            coach_bookings = Booking.query.filter_by(
                coach_id=authenticated_user.id,
                status='approved'
            ).order_by(Booking.date.desc(), Booking.start_time.desc()).all()

            # Get bookings where user is the client (only approved bookings)
            client_bookings = Booking.query.filter_by(
                client_id=authenticated_user.id,
                status='approved'
            ).order_by(Booking.date.desc(), Booking.start_time.desc()).all()

            print(f"📋 Found {len(coach_bookings)} coach bookings and {len(client_bookings)} client bookings for {authenticated_user.name}")

            return jsonify({
                'coach_bookings': [booking.to_dict() for booking in coach_bookings],
                'client_bookings': [booking.to_dict() for booking in client_bookings]
            }), 200

        except Exception as e:
            return jsonify({'error': f'Failed to get bookings: {str(e)}'}), 500

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        db.create_all()

        # Create admin user if it doesn't exist
        existing_admin = Admin.query.filter_by(email='<EMAIL>').first()
        if not existing_admin:
            admin = Admin(
                name='System Admin',
                email='<EMAIL>'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ Admin user created: <EMAIL> / admin123")
        else:
            print("ℹ️  Admin user already exists: <EMAIL>")

        # Create test approved user if it doesn't exist
        existing_user = User.query.filter_by(email='<EMAIL>').first()
        if not existing_user:
            test_user = User(
                name='John Doe',
                email='<EMAIL>',
                phone='+1234567890',
                credential='PCC Certified',
                institution='Coaching Institute',
                country='USA',
                status='approved'
            )
            test_user.set_password('password123')
            test_user.approve()
            db.session.add(test_user)
            db.session.commit()
            print("✅ Test user created: <EMAIL> / password123")
        else:
            print("ℹ️  Test user already exists: <EMAIL>")

    print("\n🚀 CoachCentral Server Starting...")
    print("📧 Admin Login: <EMAIL> / admin123")
    print("👤 User Login: <EMAIL> / password123")
    print("🌐 Server URL: http://127.0.0.1:8000")
    print("=" * 50)

    app.run(debug=True, host='127.0.0.1', port=8000)
