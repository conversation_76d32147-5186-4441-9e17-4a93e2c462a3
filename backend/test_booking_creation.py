#!/usr/bin/env python3
"""
Test script to debug booking creation issues
"""

from app import create_app
from models import db, User, Booking, AvailabilitySlot
from datetime import datetime, timedelta
import traceback

def test_booking_creation():
    app = create_app()
    with app.app_context():
        print("=== BOOKING CREATION DEBUG TEST ===")
        print()
        
        # Step 1: Check users
        print("Step 1: Checking users...")
        john = User.query.filter_by(email='<EMAIL>').first()
        vivek = User.query.filter_by(email='<EMAIL>').first()
        
        if not john:
            print("❌ John not found")
            return
        if not vivek:
            print("❌ Vivek not found")
            return
            
        print(f"✅ John: {john.name} (ID: {john.id})")
        print(f"✅ Vivek: {vivek.name} (ID: {vivek.id})")
        print()
        
        # Step 2: Update last login times
        print("Step 2: Updating last login times...")
        john.last_login = datetime.utcnow()
        vivek.last_login = datetime.utcnow()
        db.session.commit()
        print(f"✅ <PERSON>'s last login: {john.last_login}")
        print(f"✅ Vivek's last login: {vivek.last_login}")
        print()
        
        # Step 3: Check authentication token
        print("Step 3: Testing authentication token...")
        expected_token = f"auth_token_{john.id}_{john.email}"
        print(f"Expected token: {expected_token}")
        
        # Test token validity window
        recent_time = datetime.utcnow() - timedelta(hours=1)
        print(f"Recent time threshold: {recent_time}")
        print(f"John's last login is recent: {john.last_login >= recent_time}")
        print()
        
        # Step 4: Check available slots
        print("Step 4: Checking available slots...")
        available_slots = AvailabilitySlot.query.filter_by(
            coach_id=vivek.id,
            status='free'
        ).all()
        
        print(f"Found {len(available_slots)} available slots for Vivek:")
        for slot in available_slots[:3]:  # Show first 3
            print(f"  - {slot.date} {slot.start_time}-{slot.end_time} (ID: {slot.id})")
        print()
        
        if not available_slots:
            print("❌ No available slots found")
            return
            
        # Step 5: Test booking creation
        print("Step 5: Testing booking creation...")
        test_slot = available_slots[0]
        
        try:
            booking = Booking(
                coach_id=vivek.id,
                client_id=john.id,
                availability_slot_id=test_slot.id,
                date=test_slot.date,
                start_time=test_slot.start_time,
                end_time=test_slot.end_time,
                session_type='virtual',
                specialization='Test Coaching',
                notes='Debug test booking',
                status='pending'
            )
            
            print(f"Created booking object:")
            print(f"  - Coach ID: {booking.coach_id}")
            print(f"  - Client ID: {booking.client_id}")
            print(f"  - Date: {booking.date}")
            print(f"  - Time: {booking.start_time}-{booking.end_time}")
            print(f"  - Status: {booking.status}")
            
            db.session.add(booking)
            db.session.commit()
            
            print(f"✅ Booking created successfully with ID: {booking.id}")
            
            # Test booking.to_dict()
            booking_dict = booking.to_dict()
            print(f"✅ Booking to_dict() works: {booking_dict}")
            
        except Exception as e:
            print(f"❌ Error creating booking: {str(e)}")
            traceback.print_exc()
            db.session.rollback()
            
        print()
        print("=== TEST COMPLETE ===")

if __name__ == "__main__":
    test_booking_creation()
