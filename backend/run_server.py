#!/usr/bin/env python3
"""
Simple server runner with better error handling
"""

if __name__ == '__main__':
    try:
        print("🔍 Importing modules...")
        from app import create_app
        from models import db, Admin, User

        print("🚀 Starting CoachCentral Server...")

        print("🔍 Creating Flask app...")
        app = create_app()

        print("🔍 Initializing database...")
        # Initialize database
        with app.app_context():
            db.create_all()
            print("✅ Database tables created")

            # Create admin if doesn't exist
            if not Admin.query.filter_by(email='<EMAIL>').first():
                admin = Admin(name='System Admin', email='<EMAIL>')
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ Admin user created")
            else:
                print("ℹ️  Admin user already exists")

            # Create test user if doesn't exist
            if not User.query.filter_by(email='<EMAIL>').first():
                user = User(
                    name='<PERSON>',
                    email='<EMAIL>',
                    phone='+1234567890',
                    credential='PCC Certified',
                    institution='Coaching Institute',
                    country='USA',
                    status='approved'
                )
                user.set_password('password123')
                user.approve()
                db.session.add(user)
                db.session.commit()
                print("✅ Test user created")
            else:
                print("ℹ️  Test user already exists")

        print("\n📧 Login Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   User:  <EMAIL> / password123")
        print("\n🌐 Server starting on: http://127.0.0.1:8000")
        print("=" * 50)

        app.run(debug=True, host='127.0.0.1', port=8000, use_reloader=False)

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
