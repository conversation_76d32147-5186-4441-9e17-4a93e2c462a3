#!/usr/bin/env python3
"""
Test script to test the booking API directly
"""

import requests
import json
from app import create_app
from models import db, User
from datetime import datetime

def update_user_login():
    """Update user's last login time"""
    app = create_app()
    with app.app_context():
        john = User.query.filter_by(email='<EMAIL>').first()
        if john:
            john.last_login = datetime.utcnow()
            db.session.commit()
            print(f'✅ Updated John\'s last login to: {john.last_login}')
            return f'auth_token_{john.id}_{john.email}'
        return None

def test_booking_api():
    """Test the booking API"""
    print("=== BOOKING API TEST ===")
    
    # Update user login time
    token = update_user_login()
    if not token:
        print("❌ Failed to update user login")
        return
    
    print(f"Using token: {token}")
    
    # Test booking creation
    booking_data = {
        'coach_id': 3,
        'date': '2025-08-18',
        'start_time': '18:00',
        'end_time': '20:00',
        'session_type': 'virtual',
        'specialization': 'Leadership Coaching',
        'notes': 'API test booking'
    }
    
    try:
        print("Making booking request...")
        response = requests.post(
            'http://127.0.0.1:8000/api/bookings',
            headers={
                'Content-Type': 'application/json',
                'Authorization': token
            },
            json=booking_data,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 201:
            print("✅ Booking created successfully!")
        else:
            print("❌ Booking creation failed")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_booking_api()
