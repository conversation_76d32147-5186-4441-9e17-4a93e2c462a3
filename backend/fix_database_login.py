#!/usr/bin/env python3
"""
Fix database login issues by ensuring proper database setup and permissions
"""

import os
import sqlite3
from app import create_app
from models import db, User, Admin
from werkzeug.security import generate_password_hash

def fix_database_issues():
    """Fix database permissions and setup issues"""
    print("🔧 Fixing Database Login Issues...")
    print("=" * 50)
    
    # Ensure instance directory exists with proper permissions
    instance_dir = 'instance'
    if not os.path.exists(instance_dir):
        os.makedirs(instance_dir, mode=0o755)
        print(f"✅ Created instance directory: {instance_dir}")
    
    # Set proper permissions on instance directory
    os.chmod(instance_dir, 0o755)
    print(f"✅ Set permissions on {instance_dir}")
    
    db_path = os.path.join(instance_dir, 'coachcentral.db')
    
    # Remove existing database if it has permission issues
    if os.path.exists(db_path):
        try:
            # Test if we can write to the database
            conn = sqlite3.connect(db_path)
            conn.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER)")
            conn.execute("DROP TABLE IF EXISTS test_table")
            conn.commit()
            conn.close()
            print(f"✅ Database is writable: {db_path}")
        except Exception as e:
            print(f"❌ Database permission issue: {e}")
            print(f"🗑️  Removing problematic database: {db_path}")
            os.remove(db_path)
    
    # Create Flask app and initialize database
    app = create_app()
    
    with app.app_context():
        print("🔧 Creating fresh database tables...")
        
        # Drop all tables and recreate
        db.drop_all()
        db.create_all()
        
        print("✅ Database tables created successfully")
        
        # Create admin user
        print("👨‍💼 Creating admin user...")
        admin = Admin(
            name='Admin User',
            email='<EMAIL>'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        
        # Create test users
        print("👥 Creating test users...")
        
        test_users = [
            {
                'name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '1234567890',
                'country': 'USA',
                'credential': 'ACC Certified',
                'institution': 'ICF',
                'status': 'approved'
            },
            {
                'name': 'Vivek Agarwal',
                'email': '<EMAIL>',
                'phone': '9876543210',
                'country': 'India',
                'credential': 'PCC Certified',
                'institution': 'International Coach Federation',
                'status': 'approved'
            },
            {
                'name': 'Sarah Johnson',
                'email': '<EMAIL>',
                'phone': '5555555555',
                'country': 'Canada',
                'credential': 'MCC In Progress',
                'institution': 'ICF',
                'status': 'approved'
            },
            {
                'name': 'Test Pending',
                'email': '<EMAIL>',
                'phone': '1111111111',
                'country': 'UK',
                'credential': 'ACC In Progress',
                'institution': 'ICF',
                'status': 'pending'
            }
        ]
        
        for user_data in test_users:
            user = User(
                name=user_data['name'],
                email=user_data['email'],
                phone=user_data['phone'],
                country=user_data['country'],
                credential=user_data['credential'],
                institution=user_data['institution'],
                status=user_data['status']
            )
            user.set_password('password123')
            db.session.add(user)
            print(f"✅ Created user: {user_data['name']} ({user_data['status']})")
        
        # Commit all changes
        db.session.commit()
        print("✅ All users created successfully")
        
        # Set proper permissions on the database file
        if os.path.exists(db_path):
            os.chmod(db_path, 0o664)
            print(f"✅ Set database file permissions: {db_path}")
        
        # Test login functionality
        print("\n🧪 Testing Login Functionality:")
        print("-" * 30)
        
        # Test user login
        test_user = User.query.filter_by(email='<EMAIL>').first()
        if test_user:
            print(f"✅ Found test user: {test_user.name}")
            if test_user.check_password('password123'):
                print("✅ Password verification works")
            else:
                print("❌ Password verification failed")
            
            if test_user.status == 'approved':
                print("✅ User status is approved")
            else:
                print(f"❌ User status is: {test_user.status}")
        else:
            print("❌ Test user not found")
        
        # Test admin login
        test_admin = Admin.query.filter_by(email='<EMAIL>').first()
        if test_admin:
            print(f"✅ Found admin: {test_admin.name}")
            if test_admin.check_password('admin123'):
                print("✅ Admin password verification works")
            else:
                print("❌ Admin password verification failed")
        else:
            print("❌ Admin not found")
    
    print(f"\n📊 Final Database Status:")
    print(f"📁 Database location: {os.path.abspath(db_path)}")
    print(f"📊 Database size: {os.path.getsize(db_path)} bytes")
    print(f"🔒 Database permissions: {oct(os.stat(db_path).st_mode)[-3:]}")
    
    print("\n🎉 Database fix complete!")
    print("💡 Try logging in now with:")
    print("   User: <EMAIL> / password123")
    print("   Admin: <EMAIL> / admin123")

if __name__ == '__main__':
    fix_database_issues()
