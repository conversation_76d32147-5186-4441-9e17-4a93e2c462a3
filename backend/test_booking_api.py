#!/usr/bin/env python3
"""
Test the booking API directly
"""

import requests
import json
from app import create_app
from models import db, User, AvailabilitySlot
from datetime import datetime

def setup_test_data():
    """Setup test data for booking"""
    app = create_app()
    with app.app_context():
        # Update John's last login
        john = User.query.filter_by(email='<EMAIL>').first()
        if john:
            john.last_login = datetime.utcnow()
            db.session.commit()
            print(f'✅ Updated John\'s last login: {john.last_login}')
            
            # Find an available slot
            available_slot = AvailabilitySlot.query.filter_by(status='free').first()
            if available_slot:
                print(f'✅ Found available slot: Coach {available_slot.coach_id}, {available_slot.date} {available_slot.start_time}-{available_slot.end_time}')
                return {
                    'token': f'auth_token_{john.id}_{john.email}',
                    'coach_id': available_slot.coach_id,
                    'date': str(available_slot.date),
                    'start_time': str(available_slot.start_time),
                    'end_time': str(available_slot.end_time)
                }
            else:
                print('❌ No available slots found')
                return None
        else:
            print('❌ <PERSON> not found')
            return None

def test_booking_api():
    """Test the booking API"""
    print("=== TESTING BOOKING API ===")
    
    # Setup test data
    test_data = setup_test_data()
    if not test_data:
        return
    
    # Make booking request
    booking_data = {
        'coach_id': test_data['coach_id'],
        'date': test_data['date'],
        'start_time': test_data['start_time'],
        'end_time': test_data['end_time'],
        'session_type': 'virtual',
        'specialization': 'Test Coaching',
        'notes': 'API test booking'
    }
    
    print(f"Making booking request with data: {booking_data}")
    print(f"Using token: {test_data['token']}")
    
    try:
        response = requests.post(
            'http://127.0.0.1:8000/api/bookings',
            headers={
                'Content-Type': 'application/json',
                'Authorization': test_data['token']
            },
            json=booking_data,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 201:
            print("✅ Booking created successfully!")
            response_data = response.json()
            print(f"Booking ID: {response_data.get('booking', {}).get('id')}")
        else:
            print("❌ Booking creation failed")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_booking_api()
