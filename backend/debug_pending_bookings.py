#!/usr/bin/env python3
"""
Debug pending bookings to see what data is being returned
"""

from app import create_app
from models import db, Booking, User

def debug_pending_bookings():
    """Debug the pending bookings data structure"""
    print("=== DEBUGGING PENDING BOOKINGS ===")
    
    app = create_app()
    with app.app_context():
        # Find Vivek (coach with pending bookings)
        vivek = User.query.filter_by(email='<EMAIL>').first()
        if not vivek:
            print("❌ Vivek not found")
            return
            
        print(f"🔍 Checking pending bookings for coach: {vivek.name} (ID: {vivek.id})")
        
        # Get pending bookings where V<PERSON><PERSON> is the coach
        pending_bookings = Booking.query.filter_by(
            coach_id=vivek.id,
            status='pending'
        ).order_by(Booking.created_at.desc()).all()
        
        print(f"📋 Found {len(pending_bookings)} pending bookings")
        
        for i, booking in enumerate(pending_bookings, 1):
            print(f"\n--- Booking {i} (ID: {booking.id}) ---")
            print(f"Coach ID: {booking.coach_id}")
            print(f"Client ID: {booking.client_id}")
            print(f"Coach object: {booking.coach}")
            print(f"Client object: {booking.client}")
            print(f"Coach name: {booking.coach.name if booking.coach else 'None'}")
            print(f"Client name: {booking.client.name if booking.client else 'None'}")
            
            # Test the to_dict() method
            print("\n🔍 Testing to_dict() method:")
            booking_dict = booking.to_dict()
            print(f"coach_name in dict: '{booking_dict.get('coach_name')}'")
            print(f"client_name in dict: '{booking_dict.get('client_name')}'")
            
            # Show what the frontend would see
            print(f"\n📱 Frontend would display:")
            print(f"Main heading: '{booking_dict.get('client_name') or 'Unknown Client'}'")
            print(f"Client info: 'Client: {booking_dict.get('client_name') or 'Unknown Client'}'")

if __name__ == "__main__":
    debug_pending_bookings()
