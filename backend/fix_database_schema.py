#!/usr/bin/env python3
"""
Fix database schema to use unified users table
"""

import sqlite3
import os
from datetime import datetime

def fix_database_schema():
    """Fix database schema to use unified users table"""
    db_path = 'instance/coachcentral.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Fixing database schema...")
        
        # Check if availability_slots table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='availability_slots'")
        if cursor.fetchone():
            print("📋 Checking availability_slots table structure...")
            
            # Get current table structure
            cursor.execute("PRAGMA table_info(availability_slots)")
            columns = {col[1]: col[2] for col in cursor.fetchall()}
            
            if 'coach_id' not in columns:
                print("❌ availability_slots table missing coach_id column")
                
                # Drop and recreate the table with correct structure
                print("🔄 Recreating availability_slots table...")
                cursor.execute("DROP TABLE IF EXISTS availability_slots")
                
                cursor.execute("""
                    CREATE TABLE availability_slots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        coach_id INTEGER NOT NULL,
                        date DATE NOT NULL,
                        start_time TIME NOT NULL,
                        end_time TIME NOT NULL,
                        status VARCHAR(20) DEFAULT 'free' NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (coach_id) REFERENCES users (id)
                    )
                """)
                print("✅ availability_slots table recreated")
            else:
                print("✅ availability_slots table structure is correct")
        else:
            print("🔄 Creating availability_slots table...")
            cursor.execute("""
                CREATE TABLE availability_slots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coach_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    start_time TIME NOT NULL,
                    end_time TIME NOT NULL,
                    status VARCHAR(20) DEFAULT 'free' NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (coach_id) REFERENCES users (id)
                )
            """)
            print("✅ availability_slots table created")
        
        # Check if coach_availability table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='coach_availability'")
        if cursor.fetchone():
            print("📋 Checking coach_availability table structure...")
            
            # Get current table structure
            cursor.execute("PRAGMA table_info(coach_availability)")
            columns = {col[1]: col[2] for col in cursor.fetchall()}
            
            if 'coach_id' not in columns:
                print("❌ coach_availability table missing coach_id column")
                
                # Drop and recreate the table with correct structure
                print("🔄 Recreating coach_availability table...")
                cursor.execute("DROP TABLE IF EXISTS coach_availability")
                
                cursor.execute("""
                    CREATE TABLE coach_availability (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        coach_id INTEGER NOT NULL,
                        day_of_week VARCHAR(10) NOT NULL,
                        start_time TIME NOT NULL,
                        end_time TIME NOT NULL,
                        is_available BOOLEAN DEFAULT 1 NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (coach_id) REFERENCES users (id)
                    )
                """)
                print("✅ coach_availability table recreated")
            else:
                print("✅ coach_availability table structure is correct")
        else:
            print("🔄 Creating coach_availability table...")
            cursor.execute("""
                CREATE TABLE coach_availability (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coach_id INTEGER NOT NULL,
                    day_of_week VARCHAR(10) NOT NULL,
                    start_time TIME NOT NULL,
                    end_time TIME NOT NULL,
                    is_available BOOLEAN DEFAULT 1 NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (coach_id) REFERENCES users (id)
                )
            """)
            print("✅ coach_availability table created")
        
        # Check if bookings table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bookings'")
        if cursor.fetchone():
            print("📋 Checking bookings table structure...")
            
            # Get current table structure
            cursor.execute("PRAGMA table_info(bookings)")
            columns = {col[1]: col[2] for col in cursor.fetchall()}
            
            if 'coach_id' not in columns or 'client_id' not in columns:
                print("❌ bookings table missing required columns")
                
                # Drop and recreate the table with correct structure
                print("🔄 Recreating bookings table...")
                cursor.execute("DROP TABLE IF EXISTS bookings")
                
                cursor.execute("""
                    CREATE TABLE bookings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        coach_id INTEGER NOT NULL,
                        client_id INTEGER NOT NULL,
                        availability_slot_id INTEGER,
                        date DATE NOT NULL,
                        start_time TIME NOT NULL,
                        end_time TIME NOT NULL,
                        status VARCHAR(20) DEFAULT 'pending' NOT NULL,
                        session_type VARCHAR(20) DEFAULT 'virtual' NOT NULL,
                        specialization VARCHAR(100),
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        approved_at TIMESTAMP,
                        rejected_at TIMESTAMP,
                        cancelled_at TIMESTAMP,
                        FOREIGN KEY (coach_id) REFERENCES users (id),
                        FOREIGN KEY (client_id) REFERENCES users (id),
                        FOREIGN KEY (availability_slot_id) REFERENCES availability_slots (id)
                    )
                """)
                print("✅ bookings table recreated")
            else:
                print("✅ bookings table structure is correct")
        else:
            print("🔄 Creating bookings table...")
            cursor.execute("""
                CREATE TABLE bookings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coach_id INTEGER NOT NULL,
                    client_id INTEGER NOT NULL,
                    availability_slot_id INTEGER,
                    date DATE NOT NULL,
                    start_time TIME NOT NULL,
                    end_time TIME NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
                    session_type VARCHAR(20) DEFAULT 'virtual' NOT NULL,
                    specialization VARCHAR(100),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    approved_at TIMESTAMP,
                    rejected_at TIMESTAMP,
                    cancelled_at TIMESTAMP,
                    FOREIGN KEY (coach_id) REFERENCES users (id),
                    FOREIGN KEY (client_id) REFERENCES users (id),
                    FOREIGN KEY (availability_slot_id) REFERENCES availability_slots (id)
                )
            """)
            print("✅ bookings table created")
        
        # Commit changes
        conn.commit()
        print("\n🎉 Database schema fixed successfully!")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    fix_database_schema()
