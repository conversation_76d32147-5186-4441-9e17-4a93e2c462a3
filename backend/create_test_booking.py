#!/usr/bin/env python3
"""
Create a test booking to demonstrate the My Bookings functionality
"""

from app import create_app
from models import db, User, Booking
from datetime import datetime, date, time

def create_test_booking():
    """Create a test booking between two users"""
    app = create_app()
    
    with app.app_context():
        # Get two users for testing
        users = User.query.filter_by(status='approved').limit(2).all()
        
        if len(users) < 2:
            print("❌ Need at least 2 approved users for testing")
            return
        
        coach = users[0]  # First user as coach
        client = users[1]  # Second user as client
        
        print(f"👨‍🏫 Coach: {coach.name} (ID: {coach.id})")
        print(f"👤 Client: {client.name} (ID: {client.id})")
        
        # Create a test booking for tomorrow
        tomorrow = date.today().replace(day=date.today().day + 1)
        
        # Check if a similar booking already exists
        existing_booking = Booking.query.filter_by(
            coach_id=coach.id,
            client_id=client.id,
            date=tomorrow
        ).first()
        
        if existing_booking:
            print(f"📅 Existing booking found: {existing_booking.id} (Status: {existing_booking.status})")
            
            # If it's pending, approve it
            if existing_booking.status == 'pending':
                existing_booking.status = 'approved'
                existing_booking.approved_at = datetime.utcnow()
                db.session.commit()
                print(f"✅ Approved existing booking: {existing_booking.id}")
            
            return existing_booking.id
        
        # Create new test booking
        test_booking = Booking(
            coach_id=coach.id,
            client_id=client.id,
            date=tomorrow,
            start_time=time(14, 0),  # 2:00 PM
            end_time=time(16, 0),    # 4:00 PM
            status='pending',
            session_type='virtual',
            specialization='Life Coaching',
            notes='Test booking for My Bookings demonstration'
        )
        
        db.session.add(test_booking)
        db.session.commit()
        
        print(f"📝 Created test booking: {test_booking.id}")
        print(f"📅 Date: {test_booking.date}")
        print(f"⏰ Time: {test_booking.start_time} - {test_booking.end_time}")
        print(f"📊 Status: {test_booking.status}")
        
        # Now approve the booking
        test_booking.status = 'approved'
        test_booking.approved_at = datetime.utcnow()
        db.session.commit()
        
        print(f"✅ Approved test booking: {test_booking.id}")
        print(f"🎉 This booking should now appear in 'My Bookings' for both users!")
        
        return test_booking.id

def show_user_bookings():
    """Show all bookings for each user"""
    app = create_app()
    
    with app.app_context():
        users = User.query.filter_by(status='approved').all()
        
        for user in users:
            print(f"\n👤 {user.name} (ID: {user.id}) bookings:")
            
            # Coach bookings
            coach_bookings = Booking.query.filter_by(coach_id=user.id).all()
            print(f"  📚 As Coach: {len(coach_bookings)} bookings")
            for booking in coach_bookings:
                print(f"    - Booking {booking.id}: {booking.status} with {booking.client.name if booking.client else 'Unknown'}")
            
            # Client bookings  
            client_bookings = Booking.query.filter_by(client_id=user.id).all()
            print(f"  👤 As Client: {len(client_bookings)} bookings")
            for booking in client_bookings:
                print(f"    - Booking {booking.id}: {booking.status} with {booking.coach.name if booking.coach else 'Unknown'}")

if __name__ == '__main__':
    print("🎯 Creating test booking for My Bookings demonstration...")
    booking_id = create_test_booking()
    
    print("\n📊 Current booking status:")
    show_user_bookings()
    
    print(f"\n🎉 Test complete! Booking {booking_id} should appear in My Bookings for both users.")
    print("💡 To test:")
    print("   1. Login as either user")
    print("   2. Navigate to 'My Bookings'")
    print("   3. You should see the approved booking!")
