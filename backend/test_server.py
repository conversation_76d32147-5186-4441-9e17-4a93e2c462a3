#!/usr/bin/env python3
"""
Simple test server to debug the issue
"""
from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app, supports_credentials=True, origins=["http://localhost:5173", "http://localhost:5174"])

@app.route('/')
def home():
    return jsonify({'message': 'Test server is running', 'status': 'ok'})

@app.route('/api/health')
def health():
    return jsonify({'status': 'healthy'})

@app.route('/api/bookings/<int:booking_id>/approve', methods=['PUT'])
def approve_booking(booking_id):
    return jsonify({
        'message': f'Booking {booking_id} approved successfully (test)',
        'booking_id': booking_id
    })

if __name__ == '__main__':
    print("🚀 Starting test server on http://127.0.0.1:8000")
    app.run(debug=True, host='127.0.0.1', port=8000)
