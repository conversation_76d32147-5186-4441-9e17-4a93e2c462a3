<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoachCentral Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .login-form {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
        .admin-btn {
            background: #2196F3;
        }
        .admin-btn:hover {
            background: #1976D2;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 CoachCentral Login Test</h1>
        <p>Backend Server: <span id="backend-status">Checking...</span></p>
        
        <div class="login-form">
            <h3>User Login</h3>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="password123">
            <button onclick="loginUser()">Login as User</button>
            <button class="admin-btn" onclick="loginAdmin()">Login as Admin</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        // Check backend status
        async function checkBackend() {
            try {
                const response = await fetch('http://127.0.0.1:8000/');
                if (response.ok) {
                    document.getElementById('backend-status').textContent = '✅ Running';
                    document.getElementById('backend-status').style.color = '#4CAF50';
                } else {
                    document.getElementById('backend-status').textContent = '❌ Error';
                    document.getElementById('backend-status').style.color = '#f44336';
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = '❌ Not Connected';
                document.getElementById('backend-status').style.color = '#f44336';
            }
        }

        async function loginUser() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('result').innerHTML = `
                        <h3>✅ User Login Successful!</h3>
                        <p><strong>Name:</strong> ${data.user.name}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Status:</strong> ${data.user.status}</p>
                        <p><strong>Country:</strong> ${data.user.country}</p>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p>${data.error}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>❌ Connection Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function loginAdmin() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        email: '<EMAIL>', 
                        password: 'admin123' 
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('result').innerHTML = `
                        <h3>✅ Admin Login Successful!</h3>
                        <p><strong>Name:</strong> ${data.admin.name}</p>
                        <p><strong>Email:</strong> ${data.admin.email}</p>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <h3>❌ Admin Login Failed</h3>
                        <p>${data.error}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>❌ Connection Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        // Check backend status on load
        checkBackend();
    </script>
</body>
</html>
