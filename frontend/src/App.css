/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Calibri', sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

/* Banner Styles - Cobalt Blue with White Calibri Font */
.banner {
  background-color: #2E5BBA;
  color: white;
  padding: 20px 0;
  text-align: center;
  font-family: 'Calibri', sans-serif;
  font-size: 2.5rem;
  font-weight: bold;
  text-transform: lowercase;
  letter-spacing: 2px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed; /* Change to fixed for better control */
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000; /* Higher z-index to ensure it stays on top */
  height: 100px; /* Set explicit height */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Container Styles */
.container {
  max-width: 500px;
  margin: 150px auto 50px auto; /* Add top margin to account for banner */
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

/* Button Styles */
.btn-primary {
  width: 100%;
  padding: 15px;
  background-color: #2E5BBA;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Calibri', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.btn-primary:hover {
  background-color: #1e3f8a;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(46, 91, 186, 0.3);
}

.btn-secondary {
  width: 100%;
  padding: 15px;
  background-color: white;
  color: #2E5BBA;
  border: 2px solid #2E5BBA;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Calibri', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.btn-secondary:hover {
  background-color: #2E5BBA;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(46, 91, 186, 0.3);
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-family: 'Calibri', sans-serif;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2E5BBA;
  box-shadow: 0 0 0 3px rgba(46, 91, 186, 0.1);
}

/* Admin Sidebar Styles */
.admin-sidebar {
  background-color: #2E5BBA;
  color: white;
  width: 250px;
  min-height: calc(100vh - 100px); /* Adjust height to account for banner */
  padding: 20px 0;
  position: fixed;
  left: 0;
  top: 100px; /* Position below the banner */
  z-index: 50;
}

.admin-sidebar h3 {
  text-align: center;
  margin-bottom: 30px;
  font-family: 'Calibri', sans-serif;
  font-size: 1.5rem;
  padding: 0 20px;
}

.admin-sidebar-btn {
  width: 100%;
  padding: 15px 20px;
  background: none;
  border: none;
  color: white;
  text-align: left;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-left: 4px solid transparent;
}

.admin-sidebar-btn:hover,
.admin-sidebar-btn.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: white;
}

/* User Sidebar Styles */
.user-sidebar {
  background-color: #2E5BBA;
  color: white;
  width: 250px;
  min-height: calc(100vh - 100px); /* Adjust height to account for banner */
  padding: 20px 0;
  position: fixed;
  left: 0;
  top: 100px; /* Position below the banner */
  z-index: 50;
}

.user-sidebar h3 {
  text-align: center;
  margin-bottom: 30px;
  font-family: 'Calibri', sans-serif;
  font-size: 1.5rem;
  padding: 0 20px;
}

.user-sidebar-btn {
  width: 100%;
  padding: 15px 20px;
  background: none;
  border: none;
  color: white;
  text-align: left;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-left: 4px solid transparent;
}

.user-sidebar-btn:hover,
.user-sidebar-btn.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: white;
}

/* Main Content Area */
.main-content {
  margin-left: 250px;
  padding: 20px;
  padding-top: 120px; /* Add top padding to account for sticky banner */
  min-height: calc(100vh - 100px); /* Adjust height calculation */
  background-color: #f5f5f5;
}

/* User Layout Styles */
.user-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.user-header {
  background-color: #2E5BBA;
  color: white;
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.user-content {
  display: flex;
  flex: 1;
}

/* Container Form Styles */
.container-form {
  max-width: 450px;
  margin: 150px auto 50px auto; /* Add top margin to account for banner */
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

/* Header Styles */
.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-title {
  font-family: 'Calibri', sans-serif;
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
  text-transform: lowercase;
  letter-spacing: 1px;
}

.home-icon {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.home-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}



.notification-btn-large {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.notification-btn-large:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.profile-btn-large {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.profile-btn-large:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.profile-dropdown-container {
  position: relative;
}

.profile-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  margin-top: 5px;
}

.dropdown-item {
  padding: 12px 16px;
  color: #333;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.dropdown-item:first-child {
  font-weight: 600;
  color: #2E5BBA;
  cursor: default;
}

.dropdown-item:first-child:hover {
  background-color: transparent;
}

.dropdown-item:hover:not(:first-child) {
  background-color: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
  border-radius: 0 0 8px 8px;
}

/* Forgot Password Link */
.forgot-password {
  text-align: center;
  margin-top: 20px;
}

.forgot-password a {
  color: #2E5BBA;
  text-decoration: none;
  font-family: 'Calibri', sans-serif;
  font-size: 14px;
}

.forgot-password a:hover {
  text-decoration: underline;
}

/* Admin Layout Styles */
.admin-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.admin-header {
  background-color: #2E5BBA;
  color: white;
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.admin-content {
  display: flex;
  flex: 1;
}

/* User Content Area */
.user-main-content {
  flex: 1;
  padding: 30px;
  background-color: #f5f5f5;
  margin-left: 250px; /* Always account for sidebar */
  margin-top: 100px; /* Add top margin to account for sticky banner */
  min-height: calc(100vh - 100px); /* Ensure full height minus banner */
}

/* Announcements Styles */
.announcements-container {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-message {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  text-align: center;
}

.welcome-message h2 {
  color: #2E5BBA;
  font-family: 'Calibri', sans-serif;
  font-size: 2rem;
  margin-bottom: 10px;
}

.welcome-message p {
  color: #666;
  font-family: 'Calibri', sans-serif;
  font-size: 1.1rem;
}

.announcements-section h3 {
  color: #333;
  font-family: 'Calibri', sans-serif;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.announcement-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.announcement-header {
  padding: 20px 25px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.announcement-title {
  color: #2E5BBA;
  font-family: 'Calibri', sans-serif;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.announcement-date {
  color: #888;
  font-family: 'Calibri', sans-serif;
  font-size: 0.9rem;
}

.announcement-content {
  padding: 20px 25px;
  color: #555;
  font-family: 'Calibri', sans-serif;
  line-height: 1.6;
}

.announcement-actions {
  padding: 15px 25px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 20px;
  align-items: center;
}

.like-btn, .comment-btn {
  background: none;
  border: none;
  color: #2E5BBA;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: color 0.3s ease;
}

.like-btn:hover, .comment-btn:hover {
  color: #1e3f8a;
}

.comments-section {
  padding: 0 25px 20px;
  border-top: 1px solid #f0f0f0;
}

.comment-item {
  padding: 10px 0;
  border-bottom: 1px solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-user {
  font-weight: 600;
  color: #2E5BBA;
  font-family: 'Calibri', sans-serif;
  font-size: 14px;
}

.comment-text {
  color: #555;
  font-family: 'Calibri', sans-serif;
  margin: 5px 0;
}

.comment-date {
  color: #888;
  font-family: 'Calibri', sans-serif;
  font-size: 12px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding: 20px;
}

.action-btn {
  background-color: #2E5BBA;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Calibri', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background-color: #1e3f8a;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(46, 91, 186, 0.3);
}

/* My Bookings Styles */
.page-header {
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 2px solid #2E5BBA;
  margin-top: 0; /* Remove extra top margin since parent has margin-top */
}

.page-title {
  color: #2E5BBA;
  font-family: 'Calibri', sans-serif;
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
  text-align: center;
}

.bookings-section {
  margin-bottom: 40px;
}

.bookings-section-title {
  color: #333;
  font-family: 'Calibri', sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.bookings-list {
  display: grid;
  gap: 20px;
}

.booking-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.booking-card:hover {
  border-color: #2E5BBA;
  box-shadow: 0 4px 20px rgba(46, 91, 186, 0.15);
}

.booking-date {
  background: #2E5BBA;
  color: white;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  min-width: 80px;
}

.date-day {
  font-size: 1.5rem;
  font-weight: bold;
  font-family: 'Calibri', sans-serif;
}

.date-month {
  font-size: 0.9rem;
  font-family: 'Calibri', sans-serif;
  text-transform: uppercase;
}

.booking-details {
  flex: 1;
}

.client-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  font-family: 'Calibri', sans-serif;
  margin-bottom: 5px;
}

.booking-time {
  font-size: 1rem;
  color: #666;
  font-family: 'Calibri', sans-serif;
  margin-bottom: 5px;
}

.booking-specialization {
  font-size: 0.9rem;
  color: #2E5BBA;
  font-family: 'Calibri', sans-serif;
  font-weight: 500;
}

.booking-actions {
  display: flex;
  gap: 10px;
}

.edit-btn, .delete-btn, .feedback-btn, .view-feedback-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  transition: all 0.3s ease;
}

.edit-btn {
  background-color: #2E5BBA;
  color: white;
}

.edit-btn:hover {
  background-color: #1e3f8a;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.delete-btn:hover {
  background-color: #c82333;
}

.feedback-btn {
  background-color: #28a745;
  color: white;
}

.feedback-btn:hover {
  background-color: #218838;
}

.view-feedback-btn {
  background-color: #17a2b8;
  color: white;
}

.view-feedback-btn:hover {
  background-color: #138496;
}

.loading-message, .no-bookings {
  text-align: center;
  padding: 40px;
  color: #666;
  font-family: 'Calibri', sans-serif;
  font-size: 1.1rem;
}

.no-bookings {
  background: white;
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
}

/* Feedback Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.feedback-modal {
  background: white;
  border: 3px solid #FFA500;
  border-radius: 8px;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #333;
}

.modal-content {
  padding: 25px;
}

.feedback-textarea {
  width: 100%;
  min-height: 200px;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  resize: vertical;
  outline: none;
  box-sizing: border-box;
}

.feedback-textarea:focus {
  border-color: #2E5BBA;
}

.feedback-display {
  min-height: 200px;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  line-height: 1.5;
  white-space: pre-wrap;
}

.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.save-send-button {
  background-color: #2E5BBA;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
}

.save-send-button:hover {
  background-color: #1e4a9a;
}

/* New Bookings Form Styles */
.booking-form-container {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 20px;
}

.booking-form-container h2 {
  font-family: 'Calibri', sans-serif;
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin: 0 0 30px 0;
}

.booking-form-container .form-group {
  margin-bottom: 25px;
}

.booking-form-container .form-group label {
  display: block;
  font-family: 'Calibri', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.booking-form-container .form-input {
  width: 100%;
  padding: 15px;
  border: 3px solid #FFA500;
  border-radius: 4px;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  background-color: white;
  box-sizing: border-box;
}

.booking-form-container .form-input:focus {
  outline: none;
  border-color: #e6940a;
}

.booking-form-container .search-button {
  background-color: #2E5BBA;
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 4px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  margin-top: 20px;
}

.booking-form-container .search-button:hover {
  background-color: #1e4a9a;
}

/* Active sidebar button styling */
.user-sidebar-btn.active {
  background-color: #2E5BBA;
  color: white;
}

/* Search Results Styles */
.search-results-container {
  margin: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.results-header h2 {
  font-family: 'Calibri', sans-serif;
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.back-to-search-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
}

.back-to-search-btn:hover {
  background-color: #5a6268;
}

.no-results {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-results p {
  font-size: 18px;
  color: #666;
  font-family: 'Calibri', sans-serif;
}

.coach-results {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.coach-result-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.coach-result-card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.coach-date-info {
  flex-shrink: 0;
}

.date-box {
  background-color: #FFA500;
  color: white;
  padding: 10px;
  border-radius: 6px;
  text-align: center;
  min-width: 60px;
}

.date-day {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.date-month {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}

.coach-details {
  flex-grow: 1;
}

.coach-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  font-family: 'Calibri', sans-serif;
}

.coach-time {
  font-size: 16px;
  color: #666;
  font-family: 'Calibri', sans-serif;
}

.coach-actions {
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.book-btn, .about-btn {
  padding: 10px 20px;
  border: 2px solid #2E5BBA;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  transition: all 0.3s ease;
}

.book-btn {
  background-color: white;
  color: #2E5BBA;
}

.book-btn:hover {
  background-color: #2E5BBA;
  color: white;
}

.about-btn {
  background-color: white;
  color: #2E5BBA;
}

.about-btn:hover {
  background-color: #2E5BBA;
  color: white;
}

/* Approve Bookings Styles */
.approve-bookings-container {
  margin: 20px;
}

.approve-bookings-container h2 {
  font-family: 'Calibri', sans-serif;
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.no-pending-bookings {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-pending-bookings p {
  font-size: 18px;
  color: #666;
  font-family: 'Calibri', sans-serif;
}

.pending-bookings-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.pending-booking-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.pending-booking-card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.booking-date-info {
  flex-shrink: 0;
}

.booking-details {
  flex-grow: 1;
}

.booking-details .coach-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  font-family: 'Calibri', sans-serif;
}

.booking-details .booking-time {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
  font-family: 'Calibri', sans-serif;
}

.booking-details .client-info {
  font-size: 14px;
  color: #888;
  margin-bottom: 3px;
  font-family: 'Calibri', sans-serif;
}

.booking-details .specialization-info {
  font-size: 14px;
  color: #888;
  font-family: 'Calibri', sans-serif;
}

.booking-actions {
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.accept-btn, .decline-btn {
  padding: 10px 20px;
  border: 2px solid #2E5BBA;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  transition: all 0.3s ease;
  background-color: white;
  color: #2E5BBA;
}

.accept-btn:hover {
  background-color: #2E5BBA;
  color: white;
}

.decline-btn:hover {
  background-color: #2E5BBA;
  color: white;
}

/* User Profile Styles */
.profile-container {
  margin: 20px;
  max-width: 1000px;
}

.profile-container h2 {
  font-family: 'Calibri', sans-serif;
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.profile-form {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-section {
  padding: 30px;
  border-bottom: 1px solid #e0e0e0;
}

.profile-section:last-child {
  border-bottom: none;
}

.profile-section h3 {
  font-family: 'Calibri', sans-serif;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.section-description {
  color: #666;
  margin-bottom: 20px;
  font-family: 'Calibri', sans-serif;
}

/* Profile Picture Styles */
.profile-picture-section {
  display: flex;
  align-items: center;
  gap: 30px;
}

.profile-picture-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-placeholder {
  background-color: #f5f5f5;
  color: #999;
  font-family: 'Calibri', sans-serif;
  font-size: 14px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-input {
  display: none;
}

.file-input-label {
  background-color: #2E5BBA;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.file-input-label:hover {
  background-color: #1e3f8a;
}

/* Form Styles */
.form-row {
  display: flex;
  gap: 20px;
}

.form-group {
  flex: 1;
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-family: 'Calibri', sans-serif;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  font-size: 16px;
  font-family: 'Calibri', sans-serif;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #2E5BBA;
}

/* Password Section Styles */
.password-toggle {
  margin-bottom: 20px;
}

.toggle-password-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.toggle-password-btn:hover {
  background-color: #5a6268;
}

.password-fields {
  margin-top: 20px;
}

/* Weekly Hours Styles */
.weekly-hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.day-availability {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9f9f9;
}

.day-header {
  margin-bottom: 15px;
}

.day-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
}

.day-checkbox input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
}

.day-label {
  font-weight: 600;
  font-size: 18px;
  color: #333;
}

.time-inputs {
  display: flex;
  gap: 15px;
  align-items: end;
}

.time-group {
  flex: 1;
}

.time-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
  font-family: 'Calibri', sans-serif;
}

.time-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: 'Calibri', sans-serif;
}

.time-input:focus {
  outline: none;
  border-color: #2E5BBA;
}

/* Profile Actions */
.profile-actions {
  padding: 30px;
  background-color: #f8f9fa;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.save-profile-btn {
  background-color: #2E5BBA;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  transition: background-color 0.3s ease;
}

.save-profile-btn:hover {
  background-color: #1e3f8a;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'Calibri', sans-serif;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: #5a6268;
}

/* Responsive design for modals and forms */
@media (max-width: 768px) {
  .feedback-modal {
    width: 95vw;
    margin: 10px;
  }

  .modal-header, .modal-content, .modal-footer {
    padding: 15px;
  }

  .booking-form-container {
    margin: 10px;
    padding: 20px;
  }

  .booking-form-container h2 {
    font-size: 24px;
  }

  .search-results-container {
    margin: 10px;
  }

  .results-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .results-header h2 {
    font-size: 24px;
  }

  .coach-result-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .coach-actions {
    width: 100%;
    justify-content: center;
  }

  .book-btn, .about-btn {
    flex: 1;
    max-width: 150px;
  }

  .approve-bookings-container {
    margin: 10px;
  }

  .approve-bookings-container h2 {
    font-size: 24px;
  }

  .pending-booking-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .booking-actions {
    width: 100%;
    justify-content: center;
  }

  .accept-btn, .decline-btn {
    flex: 1;
    max-width: 150px;
  }

  .profile-container {
    margin: 10px;
  }

  .profile-container h2 {
    font-size: 24px;
  }

  .profile-section {
    padding: 20px;
  }

  .profile-picture-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .weekly-hours-grid {
    grid-template-columns: 1fr;
  }

  .time-inputs {
    flex-direction: column;
    gap: 10px;
  }

  .profile-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .save-profile-btn, .cancel-btn {
    width: 100%;
  }
}

/* Toggle buttons for switching between views */
.toggle-btn {
  background-color: #f8f9fa;
  border: 2px solid #dee2e6;
  color: #495057;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.toggle-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

/* Availability slots table */
.slots-table-container {
  margin-top: 20px;
  overflow-x: auto;
}

.slots-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slots-table th,
.slots-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.slots-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.slots-table tbody tr:hover {
  background-color: #f8f9fa;
}

.slots-table tbody tr:last-child td {
  border-bottom: none;
}

/* Book slot button */
.book-slot-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.book-slot-btn:hover {
  background-color: #218838;
}

.book-slot-btn:active {
  background-color: #1e7e34;
}

/* Availability Slots Section */
.availability-slots-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.availability-slots-section h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
  font-family: 'Calibri', sans-serif;
  font-weight: 600;
}

/* Filters section styling */
.filters-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Loading state */
.loading {
  text-align: center;
  padding: 20px;
  color: #666;
  font-family: 'Calibri', sans-serif;
  font-style: italic;
}

/* Coaches grid styling */
.coaches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.coach-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.coach-card:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.coach-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-family: 'Calibri', sans-serif;
  font-size: 1.3rem;
}

.coach-credential, .coach-location {
  margin: 5px 0;
  color: #666;
  font-family: 'Calibri', sans-serif;
}

.coach-availability h4 {
  margin: 15px 0 10px 0;
  color: #333;
  font-family: 'Calibri', sans-serif;
}

.availability-list {
  margin-bottom: 15px;
}

.availability-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.availability-item:last-child {
  border-bottom: none;
}

.availability-item span {
  font-family: 'Calibri', sans-serif;
  color: #555;
}

.availability-item .book-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.availability-item .book-btn:hover {
  background-color: #0056b3;
}