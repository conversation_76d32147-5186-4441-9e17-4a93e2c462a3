import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function UserHomepage() {
  const navigate = useNavigate();
  const [profileDropdownVisible, setProfileDropdownVisible] = useState(false);

  // Get current user info from localStorage (you can modify this based on your auth system)
  const currentUserName = localStorage.getItem('currentUserName') || 'FirstName LastName';

  // Sample announcements data
  const announcements = [
    {
      id: 1,
      title: 'Welcome to CoachCentral Platform',
      content: 'We are excited to announce the launch of our new coaching platform. This platform will help connect coaches with clients and provide resources for professional development.',
      date: '2024-01-15',
      likes: 12,
      comments: [
        { id: 1, user: '<PERSON>', comment: 'Great initiative! Looking forward to using this platform.', date: '2024-01-16' },
        { id: 2, user: '<PERSON>', comment: 'This looks very promising. When will all features be available?', date: '2024-01-16' }
      ]
    },
    {
      id: 2,
      title: 'New Certification Requirements',
      content: 'Please note the updated certification requirements for all coaching levels. All coaches must complete the new assessment by March 31st, 2024.',
      date: '2024-01-10',
      likes: 8,
      comments: [
        { id: 3, user: '<PERSON>', comment: 'Thanks for the update. Where can I find more details about the assessment?', date: '2024-01-11' }
      ]
    }
  ];



  const toggleProfileDropdown = () => {
    setProfileDropdownVisible(!profileDropdownVisible);
  };

  const handleLogout = () => {
    localStorage.removeItem('currentUserName');
    localStorage.removeItem('currentUserEmail');
    localStorage.removeItem('currentUserId');
    navigate('/');
  };

  const handleHomeClick = () => {
    navigate('/user-homepage');
  };

  const handleLikeAnnouncement = (id) => {
    // Handle like functionality
    console.log(`Liked announcement ${id}`);
  };

  const handleAddComment = (id) => {
    const comment = prompt('Enter your comment:');
    if (comment && comment.trim()) {
      console.log(`Added comment to announcement ${id}: ${comment}`);
      // You can implement the actual comment adding logic here
    }
  };

  const handleViewNotifications = () => {
    alert('View notifications functionality - to be implemented');
  };

  const handleViewProfile = () => {
    navigate('/user-profile');
  };

  return (
    <div className="user-layout">
      {/* Top Navigation Bar */}
      <div className="user-header">
        <div className="header-left">
          <button className="home-icon" onClick={handleHomeClick}>🏠</button>
          <h1 className="header-title">coachcentral</h1>
        </div>
        
        <div className="header-right">
          <button className="notification-btn-large" onClick={handleViewNotifications}>
            🔔
          </button>
          <div className="profile-dropdown-container">
            <button className="profile-btn-large" onClick={toggleProfileDropdown}>
              👤
            </button>
            
            {profileDropdownVisible && (
              <div className="profile-dropdown-menu">
                <div className="dropdown-item">
                  Welcome, {currentUserName}
                </div>
                <div className="dropdown-item" onClick={handleViewProfile}>
                  View Profile
                </div>
                <div className="dropdown-item" onClick={handleLogout}>
                  Logout
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="user-content">
        {/* Sidebar */}
        <div className="user-sidebar">
            <div className="sidebar-section">
              <button className="user-sidebar-btn my-bookings" onClick={() => navigate('/my-bookings')}>
                My bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn new-bookings" onClick={() => navigate('/new-bookings')}>
                New bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button
                className="user-sidebar-btn approve-bookings"
                onClick={() => navigate('/approve-bookings')}
              >
                Approve bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn my-stats">
                My stats
              </button>
            </div>
          </div>

        <div className="user-main-content with-sidebar">
          {/* Welcome Section */}
          <div className="announcements-container">
            <div className="welcome-message">
              <h2>Welcome, {currentUserName}!</h2>
              <p>Stay updated with the latest announcements and manage your coaching sessions.</p>
            </div>

            {/* Recent Announcements Section */}
            <div className="announcements-section">
              <h3>Recent Announcements</h3>
            
              {announcements.map(announcement => (
                <div key={announcement.id} className="announcement-card">
                  <div className="announcement-header">
                    <h4 className="announcement-title">{announcement.title}</h4>
                    <div className="announcement-date">{announcement.date}</div>
                  </div>

                  <div className="announcement-content">
                    <p>{announcement.content}</p>
                  </div>

                  <div className="announcement-actions">
                    <button
                      className="like-btn"
                      onClick={() => handleLikeAnnouncement(announcement.id)}
                    >
                      👍 {announcement.likes} Likes
                    </button>
                    <button
                      className="comment-btn"
                      onClick={() => handleAddComment(announcement.id)}
                    >
                      💬 {announcement.comments.length} Comments
                    </button>
                  </div>

                  {/* Comments Section */}
                  {announcement.comments && announcement.comments.length > 0 && (
                    <div className="comments-section">
                      {announcement.comments.map(comment => (
                        <div key={comment.id} className="comment-item">
                          <div className="comment-user">{comment.user}</div>
                          <div className="comment-text">{comment.comment}</div>
                          <div className="comment-date">{comment.date}</div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            <button className="action-btn" onClick={handleViewNotifications}>
              Button to view notifications
            </button>
            <button className="action-btn" onClick={handleViewProfile}>
              Button to view profile
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserHomepage;
