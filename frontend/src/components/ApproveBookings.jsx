import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiCall, isAuthenticated } from '../utils/api';

function ApproveBookings() {
  const navigate = useNavigate();
  const [profileDropdownVisible, setProfileDropdownVisible] = useState(false);
  const [pendingBookings, setPendingBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const currentUserName = localStorage.getItem('currentUserName') || 'Coach';

  // Load pending bookings from API
  const loadPendingBookings = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading pending bookings...');

      const response = await apiCall('/api/bookings/pending', {
        method: 'GET'
      });

      console.log('📡 Pending bookings API response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Pending bookings loaded:', data);
        setPendingBookings(data.pending_bookings || []);
      } else {
        console.error('❌ Failed to load pending bookings:', response.status);
        const errorData = await response.json();
        console.error('Error details:', errorData);
        setPendingBookings([]);
      }
    } catch (error) {
      console.error('❌ Error loading pending bookings:', error);
      setPendingBookings([]);
    } finally {
      setLoading(false);
    }
  };

  // Check authentication and load pending bookings on component mount
  useEffect(() => {
    console.log('🔍 ApproveBookings - Checking authentication...');
    console.log('🔍 localStorage authToken:', localStorage.getItem('authToken'));
    console.log('🔍 localStorage currentUserName:', localStorage.getItem('currentUserName'));
    console.log('🔍 isAuthenticated():', isAuthenticated());

    if (!isAuthenticated()) {
      console.log('❌ No authentication token found, redirecting to login...');
      alert('Please log in first to access this page.');
      navigate('/user-login');
      return;
    }
    loadPendingBookings();
  }, [navigate]);

  const handleHomeClick = () => {
    navigate('/user-dashboard');
  };



  const toggleProfileDropdown = () => {
    setProfileDropdownVisible(!profileDropdownVisible);
  };

  const handleViewNotifications = () => {
    alert('Notifications feature - to be implemented');
  };

  const handleViewProfile = () => {
    navigate('/user-profile');
  };

  const handleLogout = () => {
    localStorage.removeItem('currentUserName');
    localStorage.removeItem('currentUserEmail');
    localStorage.removeItem('currentUserId');
    navigate('/');
  };

  const handleAcceptBooking = async (bookingId) => {
    try {
      console.log('✅ Approving booking:', bookingId);

      const response = await apiCall(`/api/bookings/${bookingId}/approve`, {
        method: 'PUT'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Booking approved successfully:', result);
        alert('Booking approved successfully! It will now appear in your "My Bookings" page.');
        // Reload pending bookings to reflect the change
        loadPendingBookings();
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to approve booking:', errorData);
        alert(`Failed to approve booking: ${errorData.error}`);
      }
    } catch (error) {
      console.error('❌ Error approving booking:', error);
      alert('Failed to approve booking. Please try again.');
    }
  };

  const handleDeclineBooking = async (bookingId) => {
    const reason = prompt('Please provide a reason for declining this booking (optional):');

    try {
      console.log('❌ Rejecting booking:', bookingId);

      const response = await apiCall(`/api/bookings/${bookingId}/reject`, {
        method: 'PUT',
        body: JSON.stringify({ reason: reason || 'No reason provided' })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('❌ Booking rejected successfully:', result);
        alert('Booking declined successfully.');
        // Reload pending bookings to reflect the change
        loadPendingBookings();
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to reject booking:', errorData);
        alert(`Failed to decline booking: ${errorData.error}`);
      }
    } catch (error) {
      console.error('❌ Error rejecting booking:', error);
      alert('Failed to decline booking. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    return { day, month };
  };

  return (
    <div className="user-layout">
      {/* Top Navigation Bar */}
      <div className="admin-header">
        <div className="header-left">
          <button className="home-icon" onClick={handleHomeClick}>🏠</button>
          <h1 className="header-title">coachcentral</h1>
        </div>

        <div className="header-right">
          <button className="notification-btn-large" onClick={handleViewNotifications}>
            🔔
          </button>
          <div className="profile-dropdown-container">
            <button className="profile-btn-large" onClick={toggleProfileDropdown}>
              👤
            </button>

            {profileDropdownVisible && (
              <div className="profile-dropdown-menu">
                <div className="dropdown-item">
                  Welcome, {currentUserName}
                </div>
                <div className="dropdown-item" onClick={handleViewProfile}>
                  View Profile
                </div>
                <div className="dropdown-item" onClick={handleLogout}>
                  Logout
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="user-content">
        {/* Sidebar */}
        <div className="user-sidebar">
            <div className="sidebar-section">
              <button className="user-sidebar-btn my-bookings" onClick={() => navigate('/my-bookings')}>
                My bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn new-bookings" onClick={() => navigate('/new-bookings')}>
                New bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button
                className="user-sidebar-btn approve-bookings active"
                onClick={() => navigate('/approve-bookings')}
              >
                Approve bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn my-stats">
                My stats
              </button>
            </div>
          </div>

        {/* Main Content */}
        <div className="user-main-content">
          <div className="approve-bookings-container">
            <h2>Approve bookings</h2>
            
            {loading ? (
              <div className="loading-message">
                <p>Loading pending bookings...</p>
              </div>
            ) : pendingBookings.length === 0 ? (
              <div className="no-pending-bookings">
                <p>No pending bookings to approve at this time.</p>
              </div>
            ) : (
              <div className="pending-bookings-list">
                {pendingBookings.map((booking) => {
                  const { day, month } = formatDate(booking.date);
                  const timeRange = `${booking.start_time} - ${booking.end_time}`;
                  return (
                    <div key={booking.id} className="pending-booking-card">
                      <div className="booking-date-info">
                        <div className="date-box">
                          <div className="date-day">{day}</div>
                          <div className="date-month">{month}</div>
                        </div>
                      </div>

                      <div className="booking-details">
                        <div className="coach-name">{booking.client_name || 'Unknown Client'}</div>
                        <div className="booking-time">{timeRange}</div>
                        <div className="client-info">Booking request from: {booking.client_name || 'Unknown Client'}</div>
                        <div className="specialization-info">{booking.specialization || 'General Coaching'}</div>
                        {booking.notes && (
                          <div className="booking-notes">Notes: {booking.notes}</div>
                        )}
                      </div>

                      <div className="booking-actions">
                        <button
                          className="accept-btn"
                          onClick={() => handleAcceptBooking(booking.id)}
                        >
                          Accept
                        </button>
                        <button
                          className="decline-btn"
                          onClick={() => handleDeclineBooking(booking.id)}
                        >
                          Decline
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ApproveBookings;
