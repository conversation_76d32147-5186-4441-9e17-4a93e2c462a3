import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiCall } from '../utils/api';

function NewBookings() {
  const navigate = useNavigate();
  const [profileDropdownVisible, setProfileDropdownVisible] = useState(false);
  const [selectedDay, setSelectedDay] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedSpecialisation, setSelectedSpecialisation] = useState('');
  const [allCoaches, setAllCoaches] = useState([]);
  const [filteredCoaches, setFilteredCoaches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCoachForModal, setSelectedCoachForModal] = useState(null);
  const [showAllTimesModal, setShowAllTimesModal] = useState(false);

  const currentUserName = localStorage.getItem('currentUserName') || 'FirstName LastName';

  // Check authentication and load coaches
  const checkAuthAndLoadCoaches = async () => {
    try {
      console.log('🔐 Checking authentication...');
      const authResponse = await apiCall('/api/user/auth-check', {
        method: 'GET'
      });

      console.log('🔍 Auth response status:', authResponse.status);

      if (authResponse.ok) {
        const authData = await authResponse.json();
        console.log('✅ Authentication verified:', authData);
        // Wait a bit more then load coaches
        setTimeout(() => {
          loadAvailableCoaches();
        }, 200);
      } else {
        console.log('❌ Authentication failed, status:', authResponse.status);
        // Try to load coaches anyway as a fallback - maybe session is working but auth endpoint has issues
        console.log('🔄 Trying to load coaches as fallback...');
        setLoading(false); // Reset loading state first
        setTimeout(() => loadAvailableCoaches(true), 100); // Force reload
      }
    } catch (error) {
      console.error('❌ Auth check error:', error);
      // Try to load coaches anyway as a fallback
      console.log('🔄 Trying to load coaches as fallback after error...');
      setLoading(false); // Reset loading state first
      setTimeout(() => loadAvailableCoaches(true), 100); // Force reload
    }
  };

  // Load all coaches on component mount
  useEffect(() => {
    // Add a delay to ensure session is established after login
    const timer = setTimeout(() => {
      checkAuthAndLoadCoaches();
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Filter coaches when filters change
  useEffect(() => {
    filterCoaches();
  }, [selectedDay, selectedTime, selectedSpecialisation, allCoaches]);

  const loadAvailableCoaches = async (forceReload = false) => {
    // Prevent multiple simultaneous requests unless forced
    if (loading && !forceReload) {
      console.log('⏳ Already loading coaches, skipping request...');
      return;
    }

    try {
      setLoading(true);
      console.log('🔍 Loading available coaches...');
      console.log('🍪 Document cookies:', document.cookie);

      const response = await apiCall('/api/coaches/available', {
        method: 'GET'
      });

      console.log('📡 Coaches API response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Raw coaches data from API:', data);
        console.log('📊 Number of coaches:', data.coaches?.length || 0);

        // Log each coach's availability data
        data.coaches?.forEach((coach, index) => {
          console.log(`Coach ${index + 1} (${coach.name}):`, {
            id: coach.id,
            weekly_availability: coach.weekly_availability,
            available_slots: coach.available_slots
          });
        });

        setAllCoaches(data.coaches || []);
      } else {
        console.error('❌ Failed to load coaches - Status:', response.status);
        const errorText = await response.text();
        console.error('❌ Error response:', errorText);
        setAllCoaches([]);
      }
    } catch (error) {
      console.error('❌ Error loading coaches:', error);
      setAllCoaches([]);
    } finally {
      setLoading(false);
    }
  };



  const filterCoaches = () => {
    let filtered = [...allCoaches];

    // Filter by specialization (credential)
    if (selectedSpecialisation) {
      filtered = filtered.filter(coach => 
        coach.credential && coach.credential.toLowerCase().includes(selectedSpecialisation.toLowerCase())
      );
    }

    // Filter by day and time
    if (selectedDay || selectedTime) {
      filtered = filtered.filter(coach => {
        // Check weekly availability
        const hasWeeklyAvailability = coach.weekly_availability?.some(avail => {
          if (selectedDay) {
            const selectedDate = new Date(selectedDay);
            const dayOfWeek = selectedDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
            if (avail.day_of_week !== dayOfWeek) return false;
          }

          if (selectedTime) {
            const timeRangeMap = {
              '09:00-12:00': { start: '09:00', end: '12:00' },
              '12:00-15:00': { start: '12:00', end: '15:00' },
              '15:00-18:00': { start: '15:00', end: '18:00' },
              '18:00-21:00': { start: '18:00', end: '21:00' }
            };

            const timeRange = timeRangeMap[selectedTime];
            if (timeRange) {
              const availStart = avail.start_time;
              const availEnd = avail.end_time;
              // Check if availability overlaps with selected time range
              return availStart <= timeRange.end && availEnd >= timeRange.start;
            }
          }

          return true;
        });

        // Check specific available slots
        const hasSpecificSlots = coach.available_slots?.some(slot => {
          if (selectedDay && slot.date !== selectedDay) return false;
          
          if (selectedTime) {
            const timeRangeMap = {
              '09:00-12:00': { start: '09:00', end: '12:00' },
              '12:00-15:00': { start: '12:00', end: '15:00' },
              '15:00-18:00': { start: '15:00', end: '18:00' },
              '18:00-21:00': { start: '18:00', end: '21:00' }
            };

            const timeRange = timeRangeMap[selectedTime];
            if (timeRange) {
              return slot.start_time >= timeRange.start && slot.end_time <= timeRange.end;
            }
          }

          return true;
        });

        return hasWeeklyAvailability || hasSpecificSlots;
      });
    }

    setFilteredCoaches(filtered);
  };



  const toggleProfileDropdown = () => {
    setProfileDropdownVisible(!profileDropdownVisible);
  };

  const handleViewNotifications = () => {
    alert('Notifications functionality to be implemented');
  };

  const handleViewProfile = () => {
    navigate('/user-profile');
  };

  const handleHomeClick = () => {
    navigate('/user-homepage');
  };

  const handleLogout = () => {
    localStorage.removeItem('currentUserName');
    localStorage.removeItem('currentUserEmail');
    localStorage.removeItem('currentUserId');
    navigate('/');
  };

  const handleBookCoach = async (coach, availabilityInfo) => {
    try {
      const bookingData = {
        coach_id: coach.id,
        date: availabilityInfo.date,
        start_time: availabilityInfo.start_time,
        end_time: availabilityInfo.end_time,
        session_type: 'virtual',
        specialization: selectedSpecialisation || coach.credential,
        notes: `Booking request for ${coach.name}`
      };

      const response = await apiCall('/api/bookings', {
        method: 'POST',
        body: JSON.stringify(bookingData)
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Booking request sent to ${coach.name} successfully! They will review and approve/reject your request.`);
        // Refresh coaches data to update availability
        loadAvailableCoaches();
      } else {
        const errorData = await response.json();
        alert(`Failed to create booking: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error creating booking:', error);
      alert('Failed to create booking. Please try again.');
    }
  };

  const handleAboutCoach = (coach) => {
    const bio = `Coach: ${coach.name}
Email: ${coach.email}
Credential: ${coach.credential}
Institution: ${coach.institution}
Country: ${coach.country}`;
    alert(bio);
  };





  const getAvailableTimesForCoach = (coach, limitToNext4 = false) => {
    const times = [];

    console.log(`Getting available times for coach ${coach.name}:`, {
      weekly_availability: coach.weekly_availability,
      available_slots: coach.available_slots
    });

    // Add weekly availability times
    if (coach.weekly_availability && coach.weekly_availability.length > 0) {
      console.log(`Adding ${coach.weekly_availability.length} weekly availability slots`);
      coach.weekly_availability.forEach(avail => {
        times.push({
          type: 'weekly',
          day: avail.day_of_week,
          start_time: avail.start_time,
          end_time: avail.end_time,
          date: null
        });
      });
    }

    // Add specific available slots
    if (coach.available_slots && coach.available_slots.length > 0) {
      console.log(`Adding ${coach.available_slots.length} specific availability slots`);
      coach.available_slots.forEach(slot => {
        times.push({
          type: 'specific',
          date: slot.date,
          start_time: slot.start_time,
          end_time: slot.end_time,
          day: null
        });
      });
    }

    // Sort times chronologically (specific dates first, then by date/time)
    times.sort((a, b) => {
      if (a.type === 'specific' && b.type === 'specific') {
        // Both are specific dates - sort by date then time
        const dateCompare = new Date(a.date) - new Date(b.date);
        if (dateCompare !== 0) return dateCompare;
        return a.start_time.localeCompare(b.start_time);
      } else if (a.type === 'specific') {
        return -1; // Specific dates come first
      } else if (b.type === 'specific') {
        return 1;
      } else {
        // Both are weekly - sort by day of week then time
        const dayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const dayCompare = dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day);
        if (dayCompare !== 0) return dayCompare;
        return a.start_time.localeCompare(b.start_time);
      }
    });

    console.log(`Total times for ${coach.name}:`, times.length);

    // Return limited results if requested
    if (limitToNext4) {
      return times.slice(0, 4);
    }

    return times;
  };

  const handleShowMoreTimes = (coach) => {
    setSelectedCoachForModal(coach);
    setShowAllTimesModal(true);
  };

  const handleCloseModal = () => {
    setShowAllTimesModal(false);
    setSelectedCoachForModal(null);
  };

  return (
    <div className="user-layout">
      {/* Top Navigation Bar */}
      <div className="admin-header">
        <div className="header-left">
          <button className="home-icon" onClick={handleHomeClick}>🏠</button>
          <h1 className="header-title">coachcentral</h1>
        </div>

        <div className="header-right">
          <button className="notification-btn-large" onClick={handleViewNotifications}>
            🔔
          </button>
          <div className="profile-dropdown-container">
            <button className="profile-btn-large" onClick={toggleProfileDropdown}>
              👤
            </button>

            {profileDropdownVisible && (
              <div className="profile-dropdown-menu">
                <div className="dropdown-item">
                  Welcome, {currentUserName}
                </div>
                <div className="dropdown-item" onClick={handleViewProfile}>
                  View Profile
                </div>
                <div className="dropdown-item" onClick={handleLogout}>
                  Logout
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="user-content">
        {/* Sidebar */}
        <div className="user-sidebar">
            <div className="sidebar-section">
              <button className="user-sidebar-btn my-bookings" onClick={() => navigate('/my-bookings')}>
                My bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn new-bookings" onClick={() => navigate('/new-bookings')}>
                New bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn approve-bookings" onClick={() => navigate('/approve-bookings')}>
                Approve bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn my-stats">
                My stats
              </button>
            </div>
          </div>

        {/* Main Content */}
        <div className="user-main-content">
          <div className="booking-form-container">
            <h2>Available Coaches</h2>
            
            {/* Filters */}
            <div className="filters-section">
              <div className="form-group">
                <label htmlFor="day-filter">Filter by day (optional)</label>
                <input
                  type="date"
                  id="day-filter"
                  value={selectedDay}
                  onChange={(e) => setSelectedDay(e.target.value)}
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label htmlFor="time-filter">Filter by time range (optional)</label>
                <select
                  id="time-filter"
                  value={selectedTime}
                  onChange={(e) => setSelectedTime(e.target.value)}
                  className="form-input"
                >
                  <option value="">All times</option>
                  <option value="09:00-12:00">9:00 AM - 12:00 PM</option>
                  <option value="12:00-15:00">12:00 PM - 3:00 PM</option>
                  <option value="15:00-18:00">3:00 PM - 6:00 PM</option>
                  <option value="18:00-21:00">6:00 PM - 9:00 PM</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="specialisation-filter">Filter by credential (optional)</label>
                <select
                  id="specialisation-filter"
                  value={selectedSpecialisation}
                  onChange={(e) => setSelectedSpecialisation(e.target.value)}
                  className="form-input"
                >
                  <option value="">All credentials</option>
                  <option value="ACC">ACC</option>
                  <option value="PCC">PCC</option>
                  <option value="MCC">MCC</option>
                </select>
              </div>
            </div>

            {loading ? (
              <div className="loading">Loading coaches...</div>
            ) : (
              <div className="coaches-grid">
                {filteredCoaches.length === 0 ? (
                  <div className="no-results">
                    <p>No coaches found matching your criteria.</p>
                  </div>
                ) : (
                  filteredCoaches.map((coach) => {
                    const availableTimes = getAvailableTimesForCoach(coach);
                    const next4Times = getAvailableTimesForCoach(coach, true);
                    return (
                      <div key={coach.id} className="coach-card">
                        <div className="coach-header">
                          <h3>{coach.name}</h3>
                          <p className="coach-email">{coach.email}</p>
                          <p className="coach-credential">{coach.credential}</p>
                          <p className="coach-location">{coach.country}</p>
                        </div>

                        <div className="coach-availability">
                          <h4>Next Available Times:</h4>
                          {availableTimes.length === 0 ? (
                            <p>No availability set</p>
                          ) : (
                            <div className="availability-list">
                              {next4Times.map((time, index) => (
                                <div key={index} className="availability-item">
                                  {time.type === 'weekly' ? (
                                    <span>{time.day}: {time.start_time} - {time.end_time}</span>
                                  ) : (
                                    <span>{time.date}: {time.start_time} - {time.end_time}</span>
                                  )}
                                  <button
                                    className="book-btn"
                                    onClick={() => handleBookCoach(coach, time)}
                                  >
                                    Book
                                  </button>
                                </div>
                              ))}
                              {availableTimes.length > 4 && (
                                <button
                                  className="more-times-btn"
                                  onClick={() => handleShowMoreTimes(coach)}
                                >
                                  More times ({availableTimes.length - 4} more)
                                </button>
                              )}
                            </div>
                          )}
                        </div>

                        <div className="coach-actions">
                          <button
                            className="about-btn"
                            onClick={() => handleAboutCoach(coach)}
                          >
                            About Coach
                          </button>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* All Times Modal */}
      {showAllTimesModal && selectedCoachForModal && (
        <div className="modal-overlay" onClick={handleCloseModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>All Available Times - {selectedCoachForModal.name}</h3>
              <button className="modal-close-btn" onClick={handleCloseModal}>×</button>
            </div>

            <div className="modal-body">
              <p className="modal-subtitle">
                {selectedCoachForModal.email} • {selectedCoachForModal.credential} • {selectedCoachForModal.country}
              </p>

              <div className="all-times-grid">
                {getAvailableTimesForCoach(selectedCoachForModal).map((time, index) => (
                  <div key={index} className="time-slot-item">
                    <div className="time-info">
                      {time.type === 'weekly' ? (
                        <span className="time-text">
                          <strong>{time.day.charAt(0).toUpperCase() + time.day.slice(1)}</strong><br />
                          {time.start_time} - {time.end_time}
                        </span>
                      ) : (
                        <span className="time-text">
                          <strong>{new Date(time.date).toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric'
                          })}</strong><br />
                          {time.start_time} - {time.end_time}
                        </span>
                      )}
                    </div>
                    <button
                      className="book-btn modal-book-btn"
                      onClick={() => {
                        handleBookCoach(selectedCoachForModal, time);
                        handleCloseModal();
                      }}
                    >
                      Book
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default NewBookings;

// Add CSS styles for the modal and new buttons
const modalStyles = `
  .more-times-btn {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
    transition: background-color 0.2s;
  }

  .more-times-btn:hover {
    background-color: #2563eb;
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
  }

  .modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
  }

  .modal-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-close-btn:hover {
    color: #374151;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-subtitle {
    color: #6b7280;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .all-times-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .time-slot-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #f9fafb;
  }

  .time-info {
    flex: 1;
  }

  .time-text {
    font-size: 14px;
    color: #374151;
  }

  .modal-book-btn {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 10px;
  }

  .modal-book-btn:hover {
    background-color: #059669;
  }

  .availability-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
  }

  .availability-item:last-child {
    border-bottom: none;
  }

  .coach-email {
    color: #6b7280;
    font-size: 14px;
    margin: 4px 0;
    font-style: italic;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = modalStyles;
  document.head.appendChild(styleSheet);
}
