import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiCall } from '../utils/api';

function MyBookings() {
  const navigate = useNavigate();
  const [profileDropdownVisible, setProfileDropdownVisible] = useState(false);
  const [coachBookings, setCoachBookings] = useState([]);
  const [clientBookings, setClientBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSendFeedbackModal, setShowSendFeedbackModal] = useState(false);
  const [showViewFeedbackModal, setShowViewFeedbackModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [feedbackText, setFeedbackText] = useState('');

  // Get current user info from localStorage
  const currentUserName = localStorage.getItem('currentUserName') || 'FirstName LastName';

  // Load bookings from API
  const loadBookings = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading user bookings...');

      const response = await apiCall('/api/user/bookings', {
        method: 'GET'
      });

      console.log('📡 User bookings API response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ User bookings loaded:', data);
        setCoachBookings(data.coach_bookings || []);
        setClientBookings(data.client_bookings || []);
      } else {
        console.error('❌ Failed to load user bookings:', response.status);
        const errorData = await response.json();
        console.error('Error details:', errorData);
        setCoachBookings([]);
        setClientBookings([]);
      }
    } catch (error) {
      console.error('❌ Error loading user bookings:', error);
      setCoachBookings([]);
      setClientBookings([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBookings();
  }, []);



  const toggleProfileDropdown = () => {
    setProfileDropdownVisible(!profileDropdownVisible);
  };

  const handleLogout = () => {
    localStorage.removeItem('currentUserName');
    localStorage.removeItem('currentUserEmail');
    localStorage.removeItem('currentUserId');
    navigate('/');
  };

  const handleHomeClick = () => {
    navigate('/user-homepage');
  };

  const handleViewNotifications = () => {
    alert('View notifications functionality - to be implemented');
  };

  const handleViewProfile = () => {
    alert('View profile functionality - to be implemented');
  };

  const handleEditBooking = (bookingId) => {
    alert(`Edit booking ${bookingId} - functionality to be implemented`);
  };

  const handleDeleteBooking = async (bookingId) => {
    if (window.confirm('Are you sure you want to cancel this booking?')) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/bookings/${bookingId}/cancel`, {
          method: 'PUT',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          alert('Booking cancelled successfully!');
          // Reload bookings to reflect the change
          loadBookings();
        } else {
          const errorData = await response.json();
          alert(`Failed to cancel booking: ${errorData.error}`);
        }
      } catch (error) {
        console.error('Error cancelling booking:', error);
        alert('Failed to cancel booking. Please try again.');
      }
    }
  };

  const handleSendFeedback = (bookingId) => {
    // Find booking in either coach or client bookings
    const allBookings = [...coachBookings, ...clientBookings];
    const booking = allBookings.find(b => b.id === bookingId);
    setSelectedBooking(booking);
    setFeedbackText('');
    setShowSendFeedbackModal(true);
  };

  const handleViewFeedback = (bookingId) => {
    // Find booking in either coach or client bookings
    const allBookings = [...coachBookings, ...clientBookings];
    const booking = allBookings.find(b => b.id === bookingId);
    setSelectedBooking(booking);
    setShowViewFeedbackModal(true);
  };

  const handleSendFeedbackSubmit = () => {
    if (feedbackText.trim()) {
      // TODO: Implement feedback API call
      // Close modal and reset
      setShowSendFeedbackModal(false);
      setSelectedBooking(null);
      setFeedbackText('');
      alert('Feedback sent successfully!');
    } else {
      alert('Please enter feedback before sending.');
    }
  };

  const closeFeedbackModals = () => {
    setShowSendFeedbackModal(false);
    setShowViewFeedbackModal(false);
    setSelectedBooking(null);
    setFeedbackText('');
  };

  // Helper function to format date for display
  const formatDateForDisplay = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    return { day, month };
  };

  // Combine all bookings and separate into upcoming and past
  const allBookings = [...coachBookings, ...clientBookings];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const upcomingBookings = allBookings.filter(booking => {
    const bookingDate = new Date(booking.date);
    return booking.status === 'approved' && bookingDate >= today;
  });

  const pastBookings = allBookings.filter(booking => {
    const bookingDate = new Date(booking.date);
    return booking.status === 'approved' && bookingDate < today;
  });

  return (
    <div className="user-layout">
      {/* Top Navigation Bar */}
      <div className="admin-header">
        <div className="header-left">
          <button className="home-icon" onClick={handleHomeClick}>🏠</button>
          <h1 className="header-title">coachcentral</h1>
        </div>
        
        <div className="header-right">
          <button className="notification-btn-large" onClick={handleViewNotifications}>
            🔔
          </button>
          <div className="profile-dropdown-container">
            <button className="profile-btn-large" onClick={toggleProfileDropdown}>
              👤
            </button>
            
            {profileDropdownVisible && (
              <div className="profile-dropdown-menu">
                <div className="dropdown-item">
                  Welcome, {currentUserName}
                </div>
                <div className="dropdown-item" onClick={handleViewProfile}>
                  View Profile
                </div>
                <div className="dropdown-item" onClick={handleLogout}>
                  Logout
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="user-content">
        {/* Sidebar */}
        <div className="user-sidebar">
            <div className="sidebar-section">
              <button className="user-sidebar-btn my-bookings active" onClick={() => navigate('/my-bookings')}>
                My bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn new-bookings" onClick={() => navigate('/new-bookings')}>
                New bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn approve-bookings">
                Approve bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn my-stats">
                My stats
              </button>
            </div>
          </div>

        <div className="user-main-content">
          {/* Page Title */}
          <div className="page-header">
            <h1 className="page-title">My bookings</h1>
          </div>

          {/* Upcoming Bookings Section */}
          <div className="bookings-section">
            <h2 className="bookings-section-title">Upcoming</h2>
            
            {loading ? (
              <div className="loading-message">
                <p>Loading bookings...</p>
              </div>
            ) : upcomingBookings.length === 0 ? (
              <div className="no-bookings">
                <p>No upcoming bookings.</p>
              </div>
            ) : (
              <div className="bookings-list">
                {upcomingBookings.map(booking => {
                  const { day, month } = formatDateForDisplay(booking.date);
                  const timeRange = `${booking.start_time} - ${booking.end_time}`;
                  // Determine if current user is coach or client
                  const isCoach = coachBookings.some(cb => cb.id === booking.id);
                  const otherPersonName = isCoach ? booking.client_name : booking.coach_name;
                  const roleLabel = isCoach ? 'Client' : 'Coach';

                  return (
                    <div key={booking.id} className="booking-card">
                      <div className="booking-date">
                        <div className="date-day">{day}</div>
                        <div className="date-month">{month}</div>
                      </div>

                      <div className="booking-details">
                        <div className="client-name">{roleLabel}: {otherPersonName}</div>
                        <div className="booking-time">{timeRange}</div>
                        {booking.specialization && (
                          <div className="booking-specialization">{booking.specialization}</div>
                        )}
                      </div>

                      <div className="booking-actions">
                        <button
                          className="action-btn edit-btn"
                          onClick={() => handleEditBooking(booking.id)}
                        >
                          Edit
                        </button>
                        <button
                          className="action-btn delete-btn"
                          onClick={() => handleDeleteBooking(booking.id)}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Past Bookings Section */}
          <div className="bookings-section">
            <h2 className="bookings-section-title">Past</h2>
            
            <div className="bookings-list">
              {pastBookings.length === 0 ? (
                <div className="no-bookings">
                  <p>No past bookings.</p>
                </div>
              ) : (
                pastBookings.map(booking => {
                  const { day, month } = formatDateForDisplay(booking.date);
                  const timeRange = `${booking.start_time} - ${booking.end_time}`;
                  // Determine if current user is coach or client
                  const isCoach = coachBookings.some(cb => cb.id === booking.id);
                  const otherPersonName = isCoach ? booking.client_name : booking.coach_name;
                  const roleLabel = isCoach ? 'Client' : 'Coach';

                  return (
                    <div key={booking.id} className="booking-card">
                      <div className="booking-date">
                        <div className="date-day">{day}</div>
                        <div className="date-month">{month}</div>
                      </div>

                      <div className="booking-details">
                        <div className="client-name">{roleLabel}: {otherPersonName}</div>
                        <div className="booking-time">{timeRange}</div>
                        {booking.specialization && (
                          <div className="booking-specialization">{booking.specialization}</div>
                        )}
                      </div>

                      <div className="booking-actions">
                        <button
                          className="action-btn feedback-btn"
                          onClick={() => handleSendFeedback(booking.id)}
                        >
                          Send feedback
                        </button>
                        <button
                          className="action-btn view-feedback-btn"
                          onClick={() => handleViewFeedback(booking.id)}
                        >
                          View feedback
                        </button>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Send Feedback Modal */}
      {showSendFeedbackModal && (
        <div className="modal-overlay">
          <div className="feedback-modal">
            <div className="modal-header">
              <h3>Send feedback</h3>
              <button className="close-button" onClick={closeFeedbackModals}>×</button>
            </div>
            <div className="modal-content">
              <textarea
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
                placeholder="Enter your feedback here..."
                rows={8}
                className="feedback-textarea"
              />
            </div>
            <div className="modal-footer">
              <button className="save-send-button" onClick={handleSendFeedbackSubmit}>
                Save and send
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Feedback Modal */}
      {showViewFeedbackModal && (
        <div className="modal-overlay">
          <div className="feedback-modal">
            <div className="modal-header">
              <h3>View feedback</h3>
              <button className="close-button" onClick={closeFeedbackModals}>×</button>
            </div>
            <div className="modal-content">
              <div className="feedback-display">
                {selectedBooking?.feedbackText || 'No feedback available for this booking.'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MyBookings;
