import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';

function UserProfile() {
  const navigate = useNavigate();
  const [profileDropdownVisible, setProfileDropdownVisible] = useState(false);
  // Get current user info from localStorage
  const currentUserName = localStorage.getItem('currentUserName') || 'User';
  const [loading, setLoading] = useState(false);

  // Helper function to get current user ID
  const getCurrentUserId = () => {
    const userId = localStorage.getItem('currentUserId');
    console.log('🔍 UserProfile - Getting user ID from localStorage:', userId);
    return userId ? parseInt(userId) : null;
  };

  // Load user's saved availability on component mount
  useEffect(() => {
    loadUserAvailability();
  }, []);

  // Profile form state
  const [profileData, setProfileData] = useState({
    profilePicture: null,
    firstName: '',
    lastName: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    credential: '',
    country: '',
    weeklyHours: {
      monday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      },
      tuesday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      },
      wednesday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      },
      thursday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      },
      friday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      },
      saturday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      },
      sunday: {
        '08:00-10:00': false,
        '10:00-12:00': false,
        '12:00-14:00': false,
        '14:00-16:00': false,
        '16:00-18:00': false,
        '18:00-20:00': false,
        '20:00-22:00': false,
        '22:00-23:00': false
      }
    }
  });

  const [showPasswordFields, setShowPasswordFields] = useState(false);

  // Load user availability on component mount
  useEffect(() => {
    loadUserAvailability();
  }, []);

  const loadUserAvailability = async () => {
    try {
      const currentUserId = getCurrentUserId();

      if (!currentUserId) {
        console.error('No user ID found. User may not be logged in.');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/user/availability?user_id=${currentUserId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();

        // Always load basic profile information from signup data
        if (data.profileInfo) {
          setProfileData(prev => ({
            ...prev,
            firstName: data.profileInfo.firstName || '',
            lastName: data.profileInfo.lastName || '',
            email: data.profileInfo.email || '',
            credential: data.profileInfo.credential || '',
            country: data.profileInfo.country || '',
            profilePicture: data.profileInfo.profilePicture || ''
          }));
          console.log('Loaded profile info from signup data:', data.profileInfo);
        }

        // Load availability data only if it exists (otherwise keep empty for new users)
        if (data.weeklyHours) {
          // Check if user has any saved availability (any time slot is true)
          const hasAvailability = Object.values(data.weeklyHours).some(day =>
            Object.values(day).some(slot => slot === true)
          );

          if (hasAvailability) {
            setProfileData(prev => ({
              ...prev,
              weeklyHours: data.weeklyHours
            }));
            console.log('Loaded saved availability:', data.weeklyHours);
          } else {
            console.log('No availability set yet, keeping empty checkboxes');
            // Keep the default empty availability from initial state
          }
        }
      } else {
        console.log('No existing data found, using defaults');
        // If no saved data, initialize with default empty checkboxes
        resetToNewFormat();
      }
    } catch (error) {
      console.error('Error loading availability:', error);
    }
  };

  const handleHomeClick = () => {
    navigate('/user-dashboard');
  };



  const toggleProfileDropdown = () => {
    setProfileDropdownVisible(!profileDropdownVisible);
  };

  const handleViewNotifications = () => {
    alert('Notifications feature - to be implemented');
  };

  const handleViewProfile = () => {
    // Already on profile page
  };

  const handleLogout = () => {
    localStorage.removeItem('currentUserName');
    localStorage.removeItem('currentUserEmail');
    localStorage.removeItem('currentUserId');
    navigate('/');
  };

  const resetToNewFormat = () => {
    setProfileData(prev => ({
      ...prev,
      weeklyHours: {
        monday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        },
        tuesday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        },
        wednesday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        },
        thursday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        },
        friday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        },
        saturday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        },
        sunday: {
          '08:00-10:00': false,
          '10:00-12:00': false,
          '12:00-14:00': false,
          '14:00-16:00': false,
          '16:00-18:00': false,
          '18:00-20:00': false,
          '20:00-22:00': false,
          '22:00-23:00': false
        }
      }
    }));
  };

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTimeSlotChange = (day, timeSlot, checked) => {
    setProfileData(prev => ({
      ...prev,
      weeklyHours: {
        ...prev.weeklyHours,
        [day]: {
          ...prev.weeklyHours[day],
          [timeSlot]: checked
        }
      }
    }));
  };

  const handleProfilePictureChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileData(prev => ({
          ...prev,
          profilePicture: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveProfile = async () => {
    // Validate password fields if changing password
    if (showPasswordFields) {
      if (!profileData.currentPassword) {
        alert('Please enter your current password');
        return;
      }
      if (profileData.newPassword !== profileData.confirmPassword) {
        alert('New passwords do not match');
        return;
      }
      if (profileData.newPassword.length < 6) {
        alert('New password must be at least 6 characters long');
        return;
      }
    }

    try {
      setLoading(true);

      const currentUserId = getCurrentUserId();

      if (!currentUserId) {
        alert('Error: No user ID found. Please log in again.');
        return;
      }

      console.log('Saving profile for user:', currentUserId);

      // Save basic profile information
      const profileResponse = await fetch(`${API_BASE_URL}/api/user/profile`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          email: profileData.email,
          credential: profileData.credential,
          country: profileData.country,
          profilePicture: profileData.profilePicture,
          user_id: currentUserId
        }),
      });

      if (!profileResponse.ok) {
        const errorData = await profileResponse.json();
        throw new Error(errorData.error || 'Failed to save profile information');
      }

      // Save availability data
      const availabilityResponse = await fetch(`${API_BASE_URL}/api/user/availability`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          weeklyHours: profileData.weeklyHours,
          user_id: currentUserId
        }),
      });

      if (!availabilityResponse.ok) {
        const errorData = await availabilityResponse.json();
        throw new Error(errorData.error || 'Failed to save availability');
      }

      // Generate availability slots from weekly schedule
      const slotsResponse = await fetch(`${API_BASE_URL}/api/user/generate-slots`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: currentUserId
        }),
      });

      if (!slotsResponse.ok) {
        console.warn('Failed to generate availability slots, but availability was saved');
      }

      alert('Profile updated successfully! Your availability has been saved and booking slots have been generated.');
      console.log('Profile data saved:', profileData);
    } catch (error) {
      console.error('Error saving profile:', error);
      alert(`Failed to save profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const daysOfWeek = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' }
  ];

  const credentialOptions = [
    'ACC In Progress',
    'ACC Certified',
    'PCC In Progress', 
    'PCC Certified',
    'MCC In Progress',
    'MCC Certified'
  ];

  const countries = [
    'United States', 'Canada', 'United Kingdom', 'Australia', 'Germany',
    'France', 'Spain', 'Italy', 'Netherlands', 'Sweden', 'Norway',
    'Denmark', 'Switzerland', 'Austria', 'Belgium', 'Ireland', 'Other'
  ];

  return (
    <div className="user-layout">
      {/* Top Navigation Bar */}
      <div className="admin-header">
        <div className="header-left">
          <button className="home-icon" onClick={handleHomeClick}>🏠</button>
          <h1 className="header-title">coachcentral</h1>
        </div>

        <div className="header-right">
          <button className="notification-btn-large" onClick={handleViewNotifications}>
            🔔
          </button>
          <div className="profile-dropdown-container">
            <button className="profile-btn-large" onClick={toggleProfileDropdown}>
              👤
            </button>

            {profileDropdownVisible && (
              <div className="profile-dropdown-menu">
                <div className="dropdown-item">
                  Welcome, {currentUserName}
                </div>
                <div className="dropdown-item" onClick={handleViewProfile}>
                  View Profile
                </div>
                <div className="dropdown-item" onClick={handleLogout}>
                  Logout
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="user-content">
        {/* Sidebar */}
        <div className="user-sidebar">
            <div className="sidebar-section">
              <button className="user-sidebar-btn my-bookings" onClick={() => navigate('/my-bookings')}>
                My bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn new-bookings" onClick={() => navigate('/new-bookings')}>
                New bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn approve-bookings" onClick={() => navigate('/approve-bookings')}>
                Approve bookings
              </button>
            </div>

            <div className="sidebar-section">
              <button className="user-sidebar-btn my-stats">
                My stats
              </button>
            </div>
          </div>

        {/* Main Content */}
        <div className="user-main-content">
          <div className="profile-container">
            <h2>User Profile</h2>
            
            <div className="profile-form">
              {/* Profile Picture Section */}
              <div className="profile-section">
                <h3>Profile Picture</h3>
                <div className="profile-picture-section">
                  <div className="profile-picture-preview">
                    {profileData.profilePicture ? (
                      <img src={profileData.profilePicture} alt="Profile" className="profile-image" />
                    ) : (
                      <div className="profile-placeholder">
                        <span>No Image</span>
                      </div>
                    )}
                  </div>
                  <div className="profile-picture-controls">
                    <input
                      type="file"
                      id="profile-picture-input"
                      accept="image/*"
                      onChange={handleProfilePictureChange}
                      className="file-input"
                    />
                    <label htmlFor="profile-picture-input" className="file-input-label">
                      Choose Image
                    </label>
                  </div>
                </div>
              </div>

              {/* Basic Information */}
              <div className="profile-section">
                <h3>Basic Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label>First Name</label>
                    <input
                      type="text"
                      value={profileData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="form-input"
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Name</label>
                    <input
                      type="text"
                      value={profileData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="form-input"
                    />
                  </div>
                </div>
                
                <div className="form-group">
                  <label>Email</label>
                  <input
                    type="email"
                    value={profileData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="form-input"
                  />
                </div>

                <div className="form-group">
                  <label>Country</label>
                  <select
                    value={profileData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    className="form-input"
                  >
                    {countries.map(country => (
                      <option key={country} value={country}>{country}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>Credential</label>
                  <select
                    value={profileData.credential}
                    onChange={(e) => handleInputChange('credential', e.target.value)}
                    className="form-input"
                  >
                    {credentialOptions.map(credential => (
                      <option key={credential} value={credential}>{credential}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Password Section */}
              <div className="profile-section">
                <h3>Password</h3>
                <div className="password-toggle">
                  <button
                    type="button"
                    onClick={() => setShowPasswordFields(!showPasswordFields)}
                    className="toggle-password-btn"
                  >
                    {showPasswordFields ? 'Cancel Password Change' : 'Change Password'}
                  </button>
                </div>

                {showPasswordFields && (
                  <div className="password-fields">
                    <div className="form-group">
                      <label>Current Password</label>
                      <input
                        type="password"
                        value={profileData.currentPassword}
                        onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                        className="form-input"
                        placeholder="Enter current password"
                      />
                    </div>
                    <div className="form-group">
                      <label>New Password</label>
                      <input
                        type="password"
                        value={profileData.newPassword}
                        onChange={(e) => handleInputChange('newPassword', e.target.value)}
                        className="form-input"
                        placeholder="Enter new password"
                      />
                    </div>
                    <div className="form-group">
                      <label>Confirm New Password</label>
                      <input
                        type="password"
                        value={profileData.confirmPassword}
                        onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                        className="form-input"
                        placeholder="Confirm new password"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Weekly Hours Section */}
              <div className="profile-section">
                <h3>Weekly Availability</h3>
                <p className="section-description">Select your available 2-hour time slots for each day (8am - 11pm)</p>

                <div className="weekly-hours-grid">
                  {daysOfWeek.map(({ key, label }) => (
                    <div key={key} className="day-availability">
                      <div className="day-header">
                        <h4 className="day-label">{label}</h4>
                      </div>

                      <div className="time-slots-grid">
                        {Object.keys(profileData.weeklyHours[key]).map(timeSlot => (
                          <label key={timeSlot} className="time-slot-checkbox">
                            <input
                              type="checkbox"
                              checked={profileData.weeklyHours[key][timeSlot]}
                              onChange={(e) => handleTimeSlotChange(key, timeSlot, e.target.checked)}
                            />
                            <span className="time-slot-label">
                              {timeSlot.replace('-', ' - ')}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Save Button */}
              <div className="profile-actions">
                <button onClick={handleSaveProfile} className="save-profile-btn">
                  Save Profile
                </button>
                <button onClick={resetToNewFormat} className="reset-btn" style={{backgroundColor: '#f59e0b', marginRight: '10px'}}>
                  Reset Time Slots
                </button>
                <button onClick={() => navigate('/user-dashboard')} className="cancel-btn">
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserProfile;

// Add CSS for time slot checkboxes
const timeSlotStyles = `
  .time-slots-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 10px;
  }

  .time-slot-checkbox {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
  }

  .time-slot-checkbox:hover {
    background-color: #f5f5f5;
  }

  .time-slot-checkbox input[type="checkbox"] {
    margin-right: 8px;
  }

  .time-slot-checkbox input[type="checkbox"]:checked + .time-slot-label {
    font-weight: bold;
    color: #2563eb;
  }

  .day-availability {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
  }

  .day-header h4 {
    margin: 0 0 10px 0;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
  }

  .weekly-hours-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = timeSlotStyles;
  document.head.appendChild(styleSheet);
}
