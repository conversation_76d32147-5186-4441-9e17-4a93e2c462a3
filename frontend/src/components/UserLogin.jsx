import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';

function UserLogin() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      alert('Please fill in all fields');
      return;
    }

    try {
      console.log('Attempting user login...');

      const response = await fetch(`${API_BASE_URL}/api/user/login`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        })
      });

      console.log('Login response status:', response.status);
      console.log('Login response ok:', response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('Login result:', result);

        // Store user info and auth token in localStorage
        localStorage.setItem('currentUserName', result.user.name);
        localStorage.setItem('currentUserEmail', result.user.email);
        localStorage.setItem('currentUserId', result.user.id);
        if (result.auth_token) {
          localStorage.setItem('authToken', result.auth_token);
          console.log('🔑 Auth token stored:', result.auth_token.substring(0, 10) + '...');
        }
        console.log('🔍 UserLogin - Stored user data:', {
          name: result.user.name,
          email: result.user.email,
          id: result.user.id
        });

        // Navigate to user homepage
        navigate('/user-homepage');
      } else {
        const error = await response.json();
        console.log('Login error:', error);

        // Show specific error message from backend
        alert(error.error || 'Login failed');
      }
    } catch (err) {
      console.error('Login error:', err);
      alert('Login failed. Please check your connection and try again.');
    }
  };

  return (
    <div>
      <div className="banner">
        coachcentral
      </div>

      <div className="container-form">
        <h2 style={{ textAlign: 'center', marginBottom: '2rem', color: '#374151' }}>
          User Login
        </h2>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
            />
          </div>
          
          <button type="submit" className="btn-primary">
            Login
          </button>
        </form>
        
        <div className="forgot-password">
          <Link to="/forgot-password">Forgot Password?</Link>
        </div>
        
        <div style={{ textAlign: 'center', marginTop: '1rem' }}>
          <Link to="/" style={{ color: '#6b7280', textDecoration: 'none' }}>
            ← Back to Main Login
          </Link>
        </div>
      </div>
    </div>
  );
}

export default UserLogin;
