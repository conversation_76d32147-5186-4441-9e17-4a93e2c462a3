:root {
  font-family: Calib<PERSON>, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #333;
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility; 
  -webkit-font-smoothing: antialiased; 
  -moz-osx-font-smoothing: grayscale; 
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: <PERSON><PERSON>ri, inherit;
  background-color: #2563eb;
  color: white;
  cursor: pointer;
  transition: all 0.25s;
}
button:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Banner styles */
.banner {
  background-color: #1e3a8a;
  color: white;
  padding: 2rem 1rem;
  text-align: center;
  font-family: Calibri, sans-serif;
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* Container styles */
.container {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
  background: rgb(244, 178, 35);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* White container with yellow border for form pages */
.container-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  font-family: Calibri, sans-serif;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Button styles */
.btn-primary {
  background-color: #1e3a8a;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  margin-bottom: 1rem;
  transition: all 0.25s;
}

.btn-primary:hover {
  background-color: #1e40af;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #1e3a8a;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  margin-bottom: 1rem;
  transition: all 0.25s;
}

.btn-secondary:hover {
  background-color: #1e40af;
  transform: translateY(-1px);
}

/* Link styles */
.forgot-password {
  text-align: center;
  margin-top: 1rem;
}

.forgot-password a {
  color: #2563eb;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password a:hover {
  text-decoration: underline;
}

/* Success message */
.success-message {
  background-color: #1e3a8a;
  color: white;
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  margin-bottom: 1rem;
}

/* File upload styles */
.file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
}

.file-upload input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: block;
  padding: 0.75rem;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.25s;
}

.file-upload-label:hover {
  border-color: #2563eb;
  background-color: #f8fafc;
}

.file-upload-label.has-file {
  border-color: #10b981;
  background-color: #f0fdf4;
  color: #059669;
}

/* Admin Layout Styles */
.admin-layout {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.admin-header {
  background-color: #1e3a8a;
  color: white;
  padding: 2rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.25s;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.hamburger-icon {
  font-size: 1.5rem;
}

.home-icon {
  font-size: 1.8rem;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 50%;
  transition: background-color 0.25s;
}

.home-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-title {
  font-family: Calibri, sans-serif;
  font-size: 3rem;
  font-weight: bold;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-btn-large,
.profile-btn-large {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 1.8rem;
  padding: 0.75rem;
  border-radius: 50%;
  cursor: pointer;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s;
}

.notification-btn-large:hover,
.profile-btn-large:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.profile-dropdown-container {
  position: relative;
}

.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 1000;
  margin-top: 0.5rem;
}

.dropdown-item {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  color: #374151;
  transition: background-color 0.25s;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
}

.dropdown-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.dropdown-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.admin-content {
  display: flex;
  min-height: calc(100vh - 80px);
}

.sidebar {
  width: 250px;
  background-color: white;
  border-right: 3px solid rgb(244, 178, 35);
  padding: 2rem 0;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-section {
  margin-bottom: 1rem;
}

.sidebar-btn {
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  background: none;
  text-align: left;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.25s;
  border-left: 4px solid transparent;
}

.sidebar-btn:hover {
  background-color: #f8fafc;
  border-left-color: #1e3a8a;
}

.sidebar-btn.announcements {
  color: #1e3a8a;
}

.sidebar-btn.existing-users {
  color: rgb(244, 178, 35);
}

.sidebar-btn.pending-users {
  color: #1e3a8a;
}

.sidebar-btn.reports {
  color: #374151;
}

.main-content {
  flex: 1;
  padding: 3rem;
  transition: all 0.3s ease;
}

.main-content.full-width {
  margin-left: 0;
}

.welcome-section {
  max-width: 800px;
}

.welcome-title {
  font-size: 3rem;
  font-weight: bold;
  color: #374151;
  margin: 0 0 0.5rem 0;
  font-family: Calibri, sans-serif;
}

.admin-name {
  font-size: 3rem;
  font-weight: bold;
  color: #374151;
  margin: 0 0 2rem 0;
  font-family: Calibri, sans-serif;
}

.homepage-subtitle {
  font-size: 1.2rem;
  color: #6b7280;
  margin: 0;
  font-family: Calibri, sans-serif;
}

/* Admin Reports Styles */
.reports-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.reports-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #374151;
  margin: 0 0 3rem 0;
  font-family: Calibri, sans-serif;
}

.reports-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 700px;
  margin: 0 auto;
}

.report-button {
  background-color: rgb(244, 178, 35);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 2rem 1.5rem;
  font-size: 1.1rem;
  font-weight: bold;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: all 0.25s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.report-button:hover {
  background-color: rgb(234, 168, 25);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.report-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .reports-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .report-button {
    padding: 1.5rem 1rem;
    font-size: 1rem;
  }
}

/* Activity Report Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.activity-report-modal {
  background-color: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  background-color: rgb(244, 178, 35);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 5px 5px 0 0;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin: 0;
  font-family: Calibri, sans-serif;
}

.modal-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-content {
  padding: 2rem 1.5rem;
}

.period-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #374151;
  margin: 0 0 1.5rem 0;
  font-family: Calibri, sans-serif;
}

.period-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.period-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: Calibri, sans-serif;
  font-size: 1rem;
  color: #374151;
}

.period-option input[type="radio"] {
  margin-right: 0.75rem;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.period-text {
  cursor: pointer;
  user-select: none;
}

.generate-report-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: bold;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: background-color 0.2s;
  float: right;
}

.generate-report-btn:hover {
  background-color: #1d4ed8;
}

.generate-report-btn:active {
  background-color: #1e40af;
}

/* Customised Report Modal Styles */
.customised-report-modal {
  background-color: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 8px;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
}

.customised-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.field-selection-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.filter-section {
  padding-top: 0.5rem;
}

.section-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #374151;
  margin: 0 0 1rem 0;
  font-family: Calibri, sans-serif;
}

.field-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.field-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: Calibri, sans-serif;
  font-size: 1rem;
  color: #374151;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.field-checkbox:hover {
  background-color: #f9fafb;
}

.field-checkbox input[type="checkbox"] {
  margin-right: 0.75rem;
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #2563eb;
}

.checkbox-text {
  cursor: pointer;
  user-select: none;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.filter-label {
  font-weight: 500;
  color: #374151;
  font-family: Calibri, sans-serif;
  min-width: 150px;
  font-size: 1rem;
}

.filter-select,
.filter-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  font-family: Calibri, sans-serif;
  background-color: white;
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

@media (max-width: 768px) {
  .customised-report-modal {
    width: 95%;
    max-width: none;
  }

  .field-checkboxes {
    grid-template-columns: 1fr;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .filter-label {
    min-width: auto;
  }

  .filter-select,
  .filter-input {
    width: 100%;
  }
}

/* Admin Announcements Styles */
.announcements-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.post-announcement-section {
  margin-bottom: 2rem;
}

.post-announcement-btn {
  background-color: rgb(244, 178, 35);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: all 0.25s;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.post-announcement-btn:hover {
  background-color: rgb(234, 168, 25);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.new-announcement-form {
  background: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-header h3 {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.5rem;
  margin: 0 0 1.5rem 0;
}

.announcement-title-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  font-family: Calibri, sans-serif;
  box-sizing: border-box;
}

.announcement-content-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  font-family: Calibri, sans-serif;
  box-sizing: border-box;
  resize: vertical;
  min-height: 120px;
}

.announcement-title-input:focus,
.announcement-content-textarea:focus {
  outline: none;
  border-color: rgb(244, 178, 35);
  box-shadow: 0 0 0 3px rgba(244, 178, 35, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.post-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: background-color 0.2s;
}

.post-btn:hover {
  background-color: #1d4ed8;
}

.cancel-btn {
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover {
  background-color: #4b5563;
}

.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.announcement-card {
  background: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.announcement-title {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
  flex: 1;
}

.announcement-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.edit-btn,
.delete-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-btn {
  background-color: #2563eb;
  color: white;
}

.edit-btn:hover {
  background-color: #1d4ed8;
}

.delete-btn {
  background-color: #dc2626;
  color: white;
}

.delete-btn:hover {
  background-color: #b91c1c;
}

.announcement-content {
  margin-bottom: 1.5rem;
}

.announcement-content p {
  color: #4b5563;
  font-family: Calibri, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.announcement-meta {
  color: #6b7280;
  font-size: 0.9rem;
  font-family: Calibri, sans-serif;
}

.announcement-interactions {
  display: flex;
  gap: 1rem;
}

.like-btn,
.comment-btn {
  background: none;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.like-btn:hover,
.comment-btn:hover {
  border-color: rgb(244, 178, 35);
  background-color: rgba(244, 178, 35, 0.1);
}

.comments-section {
  border-top: 2px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
}

.comments-section h4 {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
}

.comment {
  background-color: rgba(244, 178, 35, 0.1);
  border-left: 4px solid rgb(244, 178, 35);
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-radius: 0 6px 6px 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.comment-user {
  color: #374151;
  font-weight: 600;
  font-family: Calibri, sans-serif;
  font-size: 0.9rem;
}

.comment-date {
  color: #6b7280;
  font-size: 0.8rem;
  font-family: Calibri, sans-serif;
}

.comment-text {
  color: #4b5563;
  font-family: Calibri, sans-serif;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsive design for announcements */
@media (max-width: 768px) {
  .announcements-container {
    padding: 1rem;
  }

  .announcement-header {
    flex-direction: column;
    align-items: stretch;
  }

  .announcement-actions {
    justify-content: flex-end;
    margin-top: 0.5rem;
  }

  .announcement-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .announcement-interactions {
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* User Homepage Styles */
.user-layout {
  min-height: 100vh;
  background-color: #f9fafb;
}

.user-content {
  display: flex;
  min-height: calc(100vh - 80px);
}

.user-sidebar {
  width: 250px;
  background-color: white;
  border-right: 2px solid #e5e7eb;
  padding: 1rem 0;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.user-sidebar-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  text-align: left;
  font-size: 1rem;
  font-family: Calibri, sans-serif;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 4px solid transparent;
}

.user-sidebar-btn:hover {
  background-color: rgba(244, 178, 35, 0.1);
  border-left-color: rgb(244, 178, 35);
}

.user-sidebar-btn.my-bookings {
  border-left-color: rgb(244, 178, 35);
  background-color: rgba(244, 178, 35, 0.1);
  font-weight: 600;
}



.user-main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 2rem;
}

.welcome-title {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.announcements-section {
  margin-bottom: 2rem;
}

.section-title {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 0 1.5rem 0;
}

.user-announcements-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.user-announcement-card {
  background: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-announcement-card .announcement-title {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0 0 0.75rem 0;
}

.user-announcement-card .announcement-text {
  color: #4b5563;
  font-family: Calibri, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.user-comment-section {
  border-top: 2px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
}

.comment-input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: rgba(244, 178, 35, 0.1);
  border: 2px solid rgb(244, 178, 35);
  border-radius: 6px;
  padding: 0.75rem;
}

.comment-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.comment-input {
  flex: 1;
  border: none;
  background: none;
  font-family: Calibri, sans-serif;
  font-size: 1rem;
  color: #374151;
  outline: none;
}

.comment-input::placeholder {
  color: #6b7280;
}

.comment-note {
  color: #6b7280;
  font-family: Calibri, sans-serif;
  font-size: 0.9rem;
  font-style: italic;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.action-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: #1d4ed8;
}

/* Responsive design for user homepage */
@media (max-width: 768px) {
  .user-content {
    flex-direction: column;
  }

  .user-sidebar {
    width: 100%;
    order: 2;
    border-right: none;
    border-top: 2px solid #e5e7eb;
    padding: 0.5rem 0;
  }

  .user-sidebar-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .user-main-content {
    order: 1;
    padding: 1rem;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
  }

  .comment-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .comment-icon {
    align-self: flex-start;
  }
}

/* My Bookings Page Styles */
.page-header {
  margin-bottom: 2rem;
}

.page-title {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.bookings-section {
  margin-bottom: 3rem;
}

.bookings-section-title {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 0 1.5rem 0;
  text-decoration: underline;
}

.bookings-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.booking-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.booking-card:hover {
  border-color: rgb(244, 178, 35);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.booking-date {
  background-color: rgb(244, 178, 35);
  color: white;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  min-width: 80px;
  flex-shrink: 0;
}

.date-day {
  font-size: 1.5rem;
  font-weight: bold;
  font-family: Calibri, sans-serif;
  line-height: 1;
}

.date-month {
  font-size: 0.9rem;
  font-family: Calibri, sans-serif;
  text-transform: uppercase;
  margin-top: 0.25rem;
}

.booking-details {
  flex: 1;
}

.client-name {
  color: #374151;
  font-family: Calibri, sans-serif;
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
}

.booking-time {
  color: #6b7280;
  font-family: Calibri, sans-serif;
  font-size: 1rem;
  margin: 0;
}

.booking-actions {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  font-family: Calibri, sans-serif;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  min-width: 120px;
  text-align: center;
}

.edit-btn {
  border-color: #2563eb;
  color: #2563eb;
}

.edit-btn:hover {
  background-color: #2563eb;
  color: white;
}

.delete-btn {
  border-color: #dc2626;
  color: #dc2626;
}

.delete-btn:hover {
  background-color: #dc2626;
  color: white;
}

.feedback-btn {
  border-color: #2563eb;
  color: #2563eb;
}

.feedback-btn:hover {
  background-color: #2563eb;
  color: white;
}

.view-feedback-btn {
  border-color: #2563eb;
  color: #2563eb;
}

.view-feedback-btn:hover {
  background-color: #2563eb;
  color: white;
}

/* Profile dropdown menu styles */
.profile-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  margin-top: 0.5rem;
}

.profile-dropdown-menu .dropdown-item {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  color: #374151;
  font-family: Calibri, sans-serif;
  transition: background-color 0.25s;
  display: block;
}

.profile-dropdown-menu .dropdown-item:hover {
  background-color: #f3f4f6;
}

.profile-dropdown-menu .dropdown-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  font-weight: 600;
  color: #1e3a8a;
}

.profile-dropdown-menu .dropdown-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* Responsive design for bookings */
@media (max-width: 768px) {
  .booking-card {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .booking-date {
    align-self: flex-start;
    min-width: 60px;
  }

  .date-day {
    font-size: 1.2rem;
  }

  .date-month {
    font-size: 0.8rem;
  }

  .booking-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    min-width: auto;
    padding: 0.5rem 1rem;
  }

  .client-name {
    font-size: 1.1rem;
  }

  .booking-time {
    font-size: 0.9rem;
  }
}

/* Admin Pending Users Styles */
.pending-users-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-title {
  font-size: 2rem;
  font-weight: bold;
  color: #374151;
  margin: 0 0 2rem 0;
  font-family: Calibri, sans-serif;
}

.users-table-container {
  background: white;
  border: 3px solid rgb(244, 178, 35);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.users-table {
  width: 100%;
  border-collapse: collapse;
  font-family: Calibri, sans-serif;
}

.users-table th {
  background-color: rgb(244, 178, 35);
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 1rem;
  border-bottom: 2px solid rgb(244, 178, 35);
}

.users-table td {
  padding: 1rem;
  border-bottom: 1px solid rgb(244, 178, 35);
  vertical-align: middle;
  font-size: 0.9rem;
}

.users-table tbody tr:hover {
  background-color: rgba(244, 178, 35, 0.05);
}

.users-table tbody tr:last-child td {
  border-bottom: none;
}

.no-data {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 2rem !important;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.approve-btn {
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.approve-btn:hover {
  background-color: #059669;
}

.reject-btn {
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reject-btn:hover {
  background-color: #b91c1c;
}

.download-btn {
  background-color: #1e3a8a;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background-color: #1e40af;
}

.delete-btn {
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.delete-btn:hover {
  background-color: #b91c1c;
  transform: scale(1.1);
}

.loading-message {
  text-align: center;
  color: #6b7280;
  font-size: 1.1rem;
  padding: 2rem;
}

.error-message {
  text-align: center;
  color: #dc2626;
  font-size: 1.1rem;
  padding: 2rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin-bottom: 2rem;
}

/* Delete Confirmation Modal Styles */
.modal-content h3 {
  color: #dc2626;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.warning-text {
  color: #dc2626;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
}

.warning-list {
  color: #374151;
  margin: 0.5rem 0 1.5rem 1rem;
  padding-left: 1rem;
}

.warning-list li {
  margin-bottom: 0.25rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.cancel-btn {
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover {
  background-color: #4b5563;
}

.confirm-delete-btn {
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirm-delete-btn:hover {
  background-color: #b91c1c;
}

/* Responsive table styles */
@media (max-width: 1024px) {
  .users-table-container {
    overflow-x: auto;
  }

  .users-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .pending-users-container {
    padding: 1rem;
  }

  .users-table th,
  .users-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .approve-btn,
  .reject-btn,
  .download-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
}
